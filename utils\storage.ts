/**
 * Local storage utilities for draft management
 */

import { DraftData, FormData } from '../types/template';

const DRAFT_KEY_PREFIX = 'draft_template_';

/**
 * Saves form data as draft to localStorage
 * @param templateId - Template ID
 * @param formData - Form data to save
 */
export function saveDraft(templateId: string, formData: FormData): void {
  try {
    const draftData: DraftData = {
      templateId,
      formData,
      lastSaved: new Date().toISOString()
    };

    const key = `${DRAFT_KEY_PREFIX}${templateId}`;
    localStorage.setItem(key, JSON.stringify(draftData));
  } catch (error) {
    console.warn('Failed to save draft to localStorage:', error);
  }
}

/**
 * Loads draft data from localStorage
 * @param templateId - Template ID
 * @returns Draft data or null if not found
 */
export function loadDraft(templateId: string): DraftData | null {
  try {
    const key = `${DRAFT_KEY_PREFIX}${templateId}`;
    const stored = localStorage.getItem(key);
    
    if (!stored) {
      return null;
    }

    const draftData: DraftData = JSON.parse(stored);
    
    // Validate the structure
    if (!draftData.templateId || !draftData.formData || !draftData.lastSaved) {
      return null;
    }

    return draftData;
  } catch (error) {
    console.warn('Failed to load draft from localStorage:', error);
    return null;
  }
}

/**
 * Removes draft data from localStorage
 * @param templateId - Template ID
 */
export function clearDraft(templateId: string): void {
  try {
    const key = `${DRAFT_KEY_PREFIX}${templateId}`;
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to clear draft from localStorage:', error);
  }
}

/**
 * Checks if a draft exists for the given template
 * @param templateId - Template ID
 * @returns True if draft exists, false otherwise
 */
export function hasDraft(templateId: string): boolean {
  try {
    const key = `${DRAFT_KEY_PREFIX}${templateId}`;
    return localStorage.getItem(key) !== null;
  } catch (error) {
    return false;
  }
}

/**
 * Gets the last saved time for a draft
 * @param templateId - Template ID
 * @returns Last saved date or null if no draft exists
 */
export function getDraftLastSaved(templateId: string): Date | null {
  const draft = loadDraft(templateId);
  return draft ? new Date(draft.lastSaved) : null;
}

/**
 * Clears all drafts from localStorage
 */
export function clearAllDrafts(): void {
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(DRAFT_KEY_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('Failed to clear all drafts from localStorage:', error);
  }
}
