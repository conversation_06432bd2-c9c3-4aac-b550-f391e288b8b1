/**
 * Keyboard navigation hook and component for enhanced accessibility
 */

import React, { useEffect, useCallback } from 'react';

interface KeyboardNavigationProps {
  onNext?: () => void;
  onPrevious?: () => void;
  onSubmit?: () => void;
  onEscape?: () => void;
  disabled?: boolean;
}

/**
 * Hook for keyboard navigation
 */
export function useKeyboardNavigation({
  onNext,
  onPrevious,
  onSubmit,
  onEscape,
  disabled = false
}: KeyboardNavigationProps) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled) return;

    // Don't interfere with form inputs
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
      // Allow Tab navigation in form fields
      if (event.key === 'Tab') {
        return;
      }
      // Allow Escape to blur form fields
      if (event.key === 'Escape') {
        target.blur();
        onEscape?.();
        return;
      }
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
      case 'Tab':
        if (!event.shiftKey) {
          event.preventDefault();
          onNext?.();
        }
        break;
      case 'ArrowUp':
        if (event.key === 'Tab' && event.shiftKey) {
          event.preventDefault();
          onPrevious?.();
        } else if (event.key === 'ArrowUp') {
          event.preventDefault();
          onPrevious?.();
        }
        break;
      case 'Enter':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          onSubmit?.();
        }
        break;
      case 'Escape':
        event.preventDefault();
        onEscape?.();
        break;
    }
  }, [onNext, onPrevious, onSubmit, onEscape, disabled]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
}

/**
 * Component wrapper for keyboard navigation
 */
const KeyboardNavigation: React.FC<KeyboardNavigationProps & { children: React.ReactNode }> = ({
  children,
  ...props
}) => {
  useKeyboardNavigation(props);
  return <>{children}</>;
};

/**
 * Focus management utilities
 */
export const focusUtils = {
  /**
   * Focus the first focusable element in a container
   */
  focusFirst: (container: HTMLElement) => {
    const focusable = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const first = focusable[0] as HTMLElement;
    if (first) {
      first.focus();
    }
  },

  /**
   * Focus the last focusable element in a container
   */
  focusLast: (container: HTMLElement) => {
    const focusable = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const last = focusable[focusable.length - 1] as HTMLElement;
    if (last) {
      last.focus();
    }
  },

  /**
   * Focus the next focusable element
   */
  focusNext: (current: HTMLElement) => {
    const focusable = Array.from(document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )) as HTMLElement[];
    
    const currentIndex = focusable.indexOf(current);
    const nextIndex = (currentIndex + 1) % focusable.length;
    focusable[nextIndex]?.focus();
  },

  /**
   * Focus the previous focusable element
   */
  focusPrevious: (current: HTMLElement) => {
    const focusable = Array.from(document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )) as HTMLElement[];
    
    const currentIndex = focusable.indexOf(current);
    const prevIndex = currentIndex === 0 ? focusable.length - 1 : currentIndex - 1;
    focusable[prevIndex]?.focus();
  }
};

export default KeyboardNavigation;
