/**
 * Background job processing utilities for the create-report Edge Function
 */

import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { BackgroundJobPayload } from './types.ts';

/**
 * Trigger background processing using Supabase Realtime channels
 */
export async function triggerRealtimeProcessing(
  supabase: SupabaseClient,
  payload: BackgroundJobPayload
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Triggering realtime processing for document ${payload.documentId}`);
    
    const channel = supabase.channel('document-processing');
    
    const { error } = await channel.send({
      type: 'broadcast',
      event: 'new-document',
      payload,
    });

    if (error) {
      console.error('Realtime broadcast error:', error);
      return { success: false, error: error.message };
    }

    console.log(`Realtime processing triggered successfully for document ${payload.documentId}`);
    return { success: true };
  } catch (error) {
    console.error('Realtime processing trigger error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Alternative: Insert job record into processing_jobs table
 * (Uncomment and use if you prefer database-based job queue)
 */
export async function triggerDatabaseJobQueue(
  supabase: SupabaseClient,
  payload: BackgroundJobPayload
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Creating database job for document ${payload.documentId}`);
    
    const { error } = await supabase
      .from('processing_jobs')
      .insert({
        document_id: payload.documentId,
        template_id: payload.templateId,
        tenant_id: payload.tenantId,
        input_data: payload.inputData,
        priority: payload.priority,
        status: 'pending',
        created_at: payload.createdAt,
        retry_count: 0,
        max_retries: 3,
      });

    if (error) {
      console.error('Database job creation error:', error);
      return { success: false, error: error.message };
    }

    console.log(`Database job created successfully for document ${payload.documentId}`);
    return { success: true };
  } catch (error) {
    console.error('Database job creation error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Alternative: Call external webhook endpoint
 * (Uncomment and use if you have an external processing service)
 */
export async function triggerWebhookProcessing(
  payload: BackgroundJobPayload,
  webhookUrl?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const url = webhookUrl || Deno.env.get('PROCESSING_WEBHOOK_URL');
    
    if (!url) {
      return { success: false, error: 'Webhook URL not configured' };
    }

    console.log(`Triggering webhook processing for document ${payload.documentId}`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get('WEBHOOK_SECRET')}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Webhook error:', response.status, errorText);
      return { 
        success: false, 
        error: `Webhook failed with status ${response.status}: ${errorText}` 
      };
    }

    console.log(`Webhook processing triggered successfully for document ${payload.documentId}`);
    return { success: true };
  } catch (error) {
    console.error('Webhook processing trigger error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Main background processing trigger with fallback options
 */
export async function triggerBackgroundProcessing(
  supabase: SupabaseClient,
  documentId: string,
  templateId: string,
  tenantId: string,
  inputData: Record<string, any>,
  priority: 'normal' | 'high' = 'normal'
): Promise<void> {
  const payload: BackgroundJobPayload = {
    documentId,
    templateId,
    tenantId,
    inputData,
    priority,
    createdAt: new Date().toISOString(),
  };

  console.log(`Triggering background processing for document ${documentId}`);

  // Primary method: Supabase Realtime
  const realtimeResult = await triggerRealtimeProcessing(supabase, payload);
  
  if (realtimeResult.success) {
    console.log(`Background processing successfully triggered via Realtime for document ${documentId}`);
    return;
  }

  console.warn(`Realtime trigger failed for document ${documentId}: ${realtimeResult.error}`);

  // Fallback method 1: Database job queue (if table exists)
  try {
    const dbResult = await triggerDatabaseJobQueue(supabase, payload);
    if (dbResult.success) {
      console.log(`Background processing successfully triggered via database for document ${documentId}`);
      return;
    }
    console.warn(`Database trigger failed for document ${documentId}: ${dbResult.error}`);
  } catch (error) {
    console.warn(`Database trigger not available for document ${documentId}:`, error);
  }

  // Fallback method 2: Webhook (if configured)
  const webhookResult = await triggerWebhookProcessing(payload);
  if (webhookResult.success) {
    console.log(`Background processing successfully triggered via webhook for document ${documentId}`);
    return;
  }

  console.error(`All background processing triggers failed for document ${documentId}`);
  console.error('Realtime error:', realtimeResult.error);
  console.error('Webhook error:', webhookResult.error);
  
  // Update document status to indicate processing trigger failure
  try {
    await supabase
      .from('documents')
      .update({
        status: 'failed',
        error_message: 'Failed to trigger background processing',
        updated_at: new Date().toISOString(),
      })
      .eq('id', documentId);
  } catch (updateError) {
    console.error(`Failed to update document status for ${documentId}:`, updateError);
  }

  // Don't throw error - document creation should succeed even if processing fails
  console.warn(`Document ${documentId} created but background processing could not be triggered`);
}

/**
 * Estimate processing time based on form data complexity
 */
export function estimateProcessingTime(
  formData: Record<string, any>,
  templateSchema: string[]
): string {
  const fieldCount = Object.keys(formData).length;
  const schemaComplexity = templateSchema.length;
  const dataSize = JSON.stringify(formData).length;

  // Simple heuristic for processing time estimation
  if (fieldCount <= 5 && schemaComplexity <= 10 && dataSize < 1000) {
    return '1-2 minutes';
  } else if (fieldCount <= 15 && schemaComplexity <= 25 && dataSize < 10000) {
    return '2-5 minutes';
  } else {
    return '5-10 minutes';
  }
}
