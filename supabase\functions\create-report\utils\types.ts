/**
 * TypeScript interfaces and types specific to the create-report Edge Function
 */

export interface CreateReportRequestBody {
  templateId: string;
  formData: Record<string, any>;
}

export interface CreateReportSuccessResponse {
  reportId: string;
  status: 'pending';
  message: string;
  estimatedProcessingTime: string;
}

export interface CreateReportErrorResponse {
  error: string;
  details: string | object;
  code: string;
}

export interface UserProfile {
  id: string;
  tenant_id: string;
  email: string;
  full_name: string | null;
  role: string;
  is_active: boolean;
}

export interface DocumentTemplate {
  id: string;
  tenant_id: string;
  name: string;
  description: string | null;
  template_type: string | null;
  schema: string[];
  storage_path: string;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  missingFields: string[];
}

export interface BackgroundJobPayload {
  documentId: string;
  templateId: string;
  tenantId: string;
  inputData: Record<string, any>;
  priority: 'normal' | 'high';
  createdAt: string;
}

// Error codes for consistent error handling
export const ErrorCodes = {
  INVALID_REQUEST: 'INVALID_REQUEST',
  AUTHENTICATION_REQUIRED: 'AUTHENTICATION_REQUIRED',
  TEMPLATE_NOT_FOUND: 'TEMPLATE_NOT_FOUND',
  TEMPLATE_ACCESS_DENIED: 'TEMPLATE_ACCESS_DENIED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  MISSING_REQUIRED_FIELDS: 'MISSING_REQUIRED_FIELDS',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  TENANT_NOT_FOUND: 'TENANT_NOT_FOUND',
  INACTIVE_TEMPLATE: 'INACTIVE_TEMPLATE',
} as const;

// HTTP Status codes
export const HttpStatus = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Processing time estimates
export const ProcessingTimeEstimates = {
  SIMPLE: '1-2 minutes',
  STANDARD: '2-5 minutes',
  COMPLEX: '5-10 minutes',
} as const;
