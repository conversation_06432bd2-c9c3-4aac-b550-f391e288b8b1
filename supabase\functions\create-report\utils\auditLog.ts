/**
 * Audit logging utilities for the create-report Edge Function
 */

import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

export interface AuditLogEntry {
  tenant_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  additional_context?: Record<string, any>;
}

/**
 * Extract client IP address from request headers
 */
export function extractClientIP(request: Request): string | null {
  // Check various headers that might contain the real client IP
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-cluster-client-ip',
    'forwarded',
  ];

  for (const header of headers) {
    const value = request.headers.get(header);
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ip = value.split(',')[0].trim();
      if (isValidIP(ip)) {
        return ip;
      }
    }
  }

  return null;
}

/**
 * Validate IP address format (basic validation)
 */
function isValidIP(ip: string): boolean {
  // Basic IPv4 validation
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (ipv4Regex.test(ip)) {
    const parts = ip.split('.');
    return parts.every(part => {
      const num = parseInt(part, 10);
      return num >= 0 && num <= 255;
    });
  }

  // Basic IPv6 validation (simplified)
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv6Regex.test(ip);
}

/**
 * Extract user agent from request headers
 */
export function extractUserAgent(request: Request): string {
  return request.headers.get('user-agent') || 'Unknown';
}

/**
 * Create comprehensive audit log entry for report creation
 */
export async function createReportCreationAuditLog(
  supabase: SupabaseClient,
  tenantId: string,
  documentId: string,
  templateId: string,
  request: Request,
  additionalContext?: Record<string, any>
): Promise<void> {
  try {
    const clientIP = extractClientIP(request);
    const userAgent = extractUserAgent(request);

    const auditEntry: AuditLogEntry = {
      tenant_id: tenantId,
      action: 'CREATE_REPORT',
      resource_type: 'document',
      resource_id: documentId,
      new_values: {
        status: 'pending',
        template_id: templateId,
        action_type: 'report_creation_requested',
        processing_triggered: true,
        ...additionalContext,
      },
      ip_address: clientIP,
      user_agent: userAgent,
      additional_context: {
        endpoint: '/functions/v1/create-report',
        method: request.method,
        timestamp: new Date().toISOString(),
        ...additionalContext,
      },
    };

    const { error } = await supabase
      .from('audit_logs')
      .insert(auditEntry);

    if (error) {
      console.error('Audit log creation error:', error);
      // Don't throw - audit logging failure shouldn't break the main flow
    } else {
      console.log(`Audit log created for document ${documentId}`);
    }
  } catch (error) {
    console.error('Audit log creation error:', error);
    // Don't throw - audit logging failure shouldn't break the main flow
  }
}

/**
 * Create audit log for validation failures
 */
export async function createValidationFailureAuditLog(
  supabase: SupabaseClient,
  tenantId: string,
  templateId: string | null,
  request: Request,
  validationErrors: any
): Promise<void> {
  try {
    const clientIP = extractClientIP(request);
    const userAgent = extractUserAgent(request);

    const auditEntry: AuditLogEntry = {
      tenant_id: tenantId,
      action: 'VALIDATION_FAILURE',
      resource_type: 'form_submission',
      resource_id: templateId || undefined,
      new_values: {
        action_type: 'form_validation_failed',
        validation_errors: validationErrors,
        timestamp: new Date().toISOString(),
      },
      ip_address: clientIP,
      user_agent: userAgent,
      additional_context: {
        endpoint: '/functions/v1/create-report',
        method: request.method,
        error_count: Array.isArray(validationErrors?.errors) ? validationErrors.errors.length : 0,
      },
    };

    const { error } = await supabase
      .from('audit_logs')
      .insert(auditEntry);

    if (error) {
      console.error('Validation failure audit log error:', error);
    } else {
      console.log('Validation failure audit log created');
    }
  } catch (error) {
    console.error('Validation failure audit log error:', error);
  }
}

/**
 * Create audit log for authentication failures
 */
export async function createAuthFailureAuditLog(
  supabase: SupabaseClient,
  request: Request,
  errorMessage: string
): Promise<void> {
  try {
    const clientIP = extractClientIP(request);
    const userAgent = extractUserAgent(request);

    // For auth failures, we might not have tenant_id, so we'll use a special value
    const auditEntry = {
      tenant_id: '00000000-0000-0000-0000-000000000000', // Special UUID for auth failures
      action: 'AUTHENTICATION_FAILURE',
      resource_type: 'auth_attempt',
      new_values: {
        action_type: 'authentication_failed',
        error_message: errorMessage,
        timestamp: new Date().toISOString(),
      },
      ip_address: clientIP,
      user_agent: userAgent,
      additional_context: {
        endpoint: '/functions/v1/create-report',
        method: request.method,
      },
    };

    const { error } = await supabase
      .from('audit_logs')
      .insert(auditEntry);

    if (error) {
      console.error('Auth failure audit log error:', error);
    } else {
      console.log('Auth failure audit log created');
    }
  } catch (error) {
    console.error('Auth failure audit log error:', error);
  }
}

/**
 * Create audit log for rate limiting (if implemented)
 */
export async function createRateLimitAuditLog(
  supabase: SupabaseClient,
  tenantId: string,
  request: Request,
  limitType: string,
  currentCount: number,
  limit: number
): Promise<void> {
  try {
    const clientIP = extractClientIP(request);
    const userAgent = extractUserAgent(request);

    const auditEntry: AuditLogEntry = {
      tenant_id: tenantId,
      action: 'RATE_LIMIT_EXCEEDED',
      resource_type: 'rate_limit',
      new_values: {
        action_type: 'rate_limit_exceeded',
        limit_type: limitType,
        current_count: currentCount,
        limit: limit,
        timestamp: new Date().toISOString(),
      },
      ip_address: clientIP,
      user_agent: userAgent,
      additional_context: {
        endpoint: '/functions/v1/create-report',
        method: request.method,
      },
    };

    const { error } = await supabase
      .from('audit_logs')
      .insert(auditEntry);

    if (error) {
      console.error('Rate limit audit log error:', error);
    } else {
      console.log('Rate limit audit log created');
    }
  } catch (error) {
    console.error('Rate limit audit log error:', error);
  }
}
