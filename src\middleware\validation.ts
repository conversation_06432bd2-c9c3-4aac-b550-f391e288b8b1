/**
 * Request validation middleware
 */

import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Schema for create report request
const createReportSchema = z.object({
  templateId: z.string().uuid('Template ID must be a valid UUID'),
  formData: z.record(z.string(), z.string()).refine(
    (data) => Object.keys(data).length > 0,
    'Form data cannot be empty'
  )
});

/**
 * Validates create report request body
 */
export function validateCreateReport(req: Request, res: Response, next: NextFunction) {
  try {
    const result = createReportSchema.safeParse(req.body);
    
    if (!result.success) {
      const errors = result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    // Validation passed, continue to next middleware
    next();
  } catch (error) {
    console.error('Validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Validation failed'
    });
  }
}

/**
 * Generic validation middleware factory
 */
export function validateSchema(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }));

        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors
        });
      }

      next();
    } catch (error) {
      console.error('Validation error:', error);
      res.status(500).json({
        success: false,
        error: 'Validation failed'
      });
    }
  };
}
