/**
 * Live form preview component
 */

import React, { useState } from 'react';
import { FormData } from '../types/template';
import { formatFieldLabel } from '../utils/fieldUtils';

interface FormPreviewProps {
  formData: FormData;
  templateName: string;
  className?: string;
  onClose?: () => void;
}

const FormPreview: React.FC<FormPreviewProps> = ({
  formData,
  templateName,
  className = '',
  onClose
}) => {
  const [viewMode, setViewMode] = useState<'formatted' | 'json'>('formatted');

  const filledFields = Object.entries(formData).filter(([_, value]) => 
    value && value.trim() !== ''
  );

  const emptyFields = Object.entries(formData).filter(([_, value]) => 
    !value || value.trim() === ''
  );

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          Form Preview
        </h3>
        <div className="flex items-center space-x-2">
          {/* View mode toggle */}
          <div className="flex rounded-md shadow-sm">
            <button
              type="button"
              onClick={() => setViewMode('formatted')}
              className={`px-3 py-1 text-sm font-medium rounded-l-md border ${
                viewMode === 'formatted'
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              Formatted
            </button>
            <button
              type="button"
              onClick={() => setViewMode('json')}
              className={`px-3 py-1 text-sm font-medium rounded-r-md border-t border-r border-b ${
                viewMode === 'json'
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              JSON
            </button>
          </div>
          
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              aria-label="Close preview"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {viewMode === 'formatted' ? (
          <div className="space-y-4">
            {/* Template info */}
            <div className="pb-3 border-b border-gray-100">
              <h4 className="text-sm font-medium text-gray-900">
                Template: {templateName}
              </h4>
              <p className="text-xs text-gray-500 mt-1">
                Generated on {new Date().toLocaleString()}
              </p>
            </div>

            {/* Filled fields */}
            {filledFields.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2">
                  Completed Fields ({filledFields.length})
                </h5>
                <div className="space-y-2">
                  {filledFields.map(([fieldName, value]) => (
                    <div key={fieldName} className="flex flex-col space-y-1">
                      <dt className="text-xs font-medium text-gray-600">
                        {formatFieldLabel(fieldName)}
                      </dt>
                      <dd className="text-sm text-gray-900 bg-gray-50 px-2 py-1 rounded">
                        {value}
                      </dd>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Empty fields */}
            {emptyFields.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-gray-600 mb-2">
                  Remaining Fields ({emptyFields.length})
                </h5>
                <div className="grid grid-cols-2 gap-1">
                  {emptyFields.map(([fieldName]) => (
                    <div key={fieldName} className="text-xs text-gray-500">
                      {formatFieldLabel(fieldName)}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Summary */}
            <div className="pt-3 border-t border-gray-100">
              <div className="flex justify-between text-xs text-gray-600">
                <span>Total Fields: {Object.keys(formData).length}</span>
                <span>Completed: {filledFields.length}</span>
                <span>Remaining: {emptyFields.length}</span>
              </div>
            </div>
          </div>
        ) : (
          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">
              Raw JSON Data
            </h5>
            <pre className="text-xs text-gray-600 bg-gray-50 p-3 rounded overflow-auto max-h-64">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default FormPreview;
