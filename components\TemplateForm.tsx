/**
 * Dynamic Form Generator Component for Document Template Processing
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { TemplateFormProps, Template, FormData } from '../types/template';
import { FormFieldType } from '../types/common';
import { fetchTemplate, createReport, sanitizeTemplateSchema } from '../services/templateService';
import { 
  detectFieldType, 
  formatFieldLabel, 
  calculateProgress,
  generatePlaceholder 
} from '../utils/fieldUtils';
import { validateForm, isFormValid } from '../utils/validation';
import { saveDraft, loadDraft, clearDraft } from '../utils/storage';
import DynamicField from './DynamicField';
import LoadingSpinner from './ui/LoadingSpinner';
import ErrorMessage from './ui/ErrorMessage';

const TemplateForm: React.FC<TemplateFormProps> = ({
  templateId,
  onSuccess,
  onError,
  className = ''
}) => {
  // State management
  const [template, setTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<FormData>({});
  const [fieldTypes, setFieldTypes] = useState<Record<string, FormFieldType>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [progress, setProgress] = useState(0);

  // Refs for auto-save
  const autoSaveIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedRef = useRef<string>('');

  /**
   * Loads template data and initializes form
   */
  const loadTemplate = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadError(null);

      const templateData = await fetchTemplate(templateId);
      setTemplate(templateData);

      // Sanitize and process schema
      const cleanSchema = sanitizeTemplateSchema(templateData.schema);
      
      // Detect field types
      const types: Record<string, FormFieldType> = {};
      cleanSchema.forEach(fieldName => {
        const detection = detectFieldType(fieldName);
        types[fieldName] = detection.type;
      });
      setFieldTypes(types);

      // Load draft data if available
      const draft = loadDraft(templateId);
      if (draft && draft.formData) {
        setFormData(draft.formData);
        lastSavedRef.current = JSON.stringify(draft.formData);
      } else {
        // Initialize empty form data
        const initialData: FormData = {};
        cleanSchema.forEach(fieldName => {
          initialData[fieldName] = '';
        });
        setFormData(initialData);
        lastSavedRef.current = JSON.stringify(initialData);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load template';
      setLoadError(errorMessage);
      onError?.(new Error(errorMessage));
    } finally {
      setIsLoading(false);
    }
  }, [templateId, onError]);

  /**
   * Auto-save functionality
   */
  const autoSave = useCallback(() => {
    const currentData = JSON.stringify(formData);
    if (currentData !== lastSavedRef.current) {
      saveDraft(templateId, formData);
      lastSavedRef.current = currentData;
    }
  }, [templateId, formData]);

  /**
   * Handles field value changes
   */
  const handleFieldChange = useCallback((fieldName: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Clear field error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  }, [errors]);

  /**
   * Handles field blur for validation
   */
  const handleFieldBlur = useCallback((fieldName: string) => {
    const value = formData[fieldName] || '';
    const fieldType = fieldTypes[fieldName];
    
    if (fieldType) {
      const error = validateForm(
        { [fieldName]: value },
        { [fieldName]: fieldType },
        [fieldName] // All fields are required
      );
      
      if (error.length > 0) {
        setErrors(prev => ({
          ...prev,
          [fieldName]: error[0].message
        }));
      }
    }
  }, [formData, fieldTypes]);

  /**
   * Handles form submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!template) return;

    try {
      setIsSubmitting(true);
      setErrors({});

      // Validate all fields
      const schema = sanitizeTemplateSchema(template.schema);
      const validationErrors = validateForm(formData, fieldTypes, schema);
      
      if (validationErrors.length > 0) {
        const errorMap: Record<string, string> = {};
        validationErrors.forEach(error => {
          errorMap[error.field] = error.message;
        });
        setErrors(errorMap);
        return;
      }

      // Submit form data
      const response = await createReport({
        templateId,
        formData
      });

      if (response.success && response.reportId) {
        // Clear draft on successful submission
        clearDraft(templateId);
        onSuccess?.(response.reportId);
      } else {
        throw new Error(response.error || 'Failed to create report');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit form';
      onError?.(new Error(errorMessage));
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Clears form data
   */
  const handleClearForm = useCallback(() => {
    if (!template) return;

    const schema = sanitizeTemplateSchema(template.schema);
    const emptyData: FormData = {};
    schema.forEach(fieldName => {
      emptyData[fieldName] = '';
    });
    
    setFormData(emptyData);
    setErrors({});
    clearDraft(templateId);
  }, [template, templateId]);

  // Effects
  useEffect(() => {
    loadTemplate();
  }, [loadTemplate]);

  useEffect(() => {
    if (template) {
      const schema = sanitizeTemplateSchema(template.schema);
      const newProgress = calculateProgress(formData, schema.length);
      setProgress(newProgress);
    }
  }, [formData, template]);

  useEffect(() => {
    // Set up auto-save interval
    autoSaveIntervalRef.current = setInterval(autoSave, 30000); // 30 seconds

    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, [autoSave]);

  // Loading state
  if (isLoading) {
    return (
      <div className={`p-6 ${className}`}>
        <LoadingSpinner size="lg" message="Loading template..." />
      </div>
    );
  }

  // Error state
  if (loadError) {
    return (
      <div className={`p-6 ${className}`}>
        <ErrorMessage
          title="Failed to Load Template"
          message={loadError}
          onRetry={loadTemplate}
        />
      </div>
    );
  }

  // No template state
  if (!template) {
    return (
      <div className={`p-6 ${className}`}>
        <ErrorMessage
          title="Template Not Found"
          message="The requested template could not be found."
        />
      </div>
    );
  }

  const schema = sanitizeTemplateSchema(template.schema);
  const formIsValid = isFormValid(formData, fieldTypes, schema);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {template.name}
            </h2>
            {template.description && (
              <p className="text-sm text-gray-600 mt-1">
                {template.description}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-4">
            {/* Progress indicator */}
            <div className="text-sm text-gray-600">
              Progress: {progress}%
            </div>
            <div className="w-24 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6">
        <div className="space-y-6">
          {schema.map(fieldName => {
            const fieldType = fieldTypes[fieldName];
            const label = formatFieldLabel(fieldName);
            const placeholder = generatePlaceholder(fieldName, fieldType);
            
            return (
              <DynamicField
                key={fieldName}
                name={fieldName}
                label={label}
                type={fieldType}
                value={formData[fieldName] || ''}
                onChange={(value) => handleFieldChange(fieldName, value)}
                onBlur={() => handleFieldBlur(fieldName)}
                error={errors[fieldName]}
                placeholder={placeholder}
                required={true}
              />
            );
          })}
        </div>

        {/* Actions */}
        <div className="mt-8 flex items-center justify-between">
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleClearForm}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Clear Form
            </button>
            <button
              type="button"
              onClick={() => setShowPreview(!showPreview)}
              className="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {showPreview ? 'Hide' : 'Show'} Preview
            </button>
          </div>
          
          <button
            type="submit"
            disabled={!formIsValid || isSubmitting}
            className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isSubmitting && <LoadingSpinner size="sm" />}
            <span>{isSubmitting ? 'Generating...' : 'Generate Document'}</span>
          </button>
        </div>

        {/* Live Preview */}
        {showPreview && (
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              Form Data Preview
            </h3>
            <pre className="text-xs text-gray-600 overflow-auto">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </div>
        )}
      </form>
    </div>
  );
};

export default TemplateForm;
