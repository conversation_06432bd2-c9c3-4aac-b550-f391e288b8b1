/**
 * Accessibility tests for components
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';
import TemplateForm from '../../components/TemplateForm';
import DynamicField from '../../components/DynamicField';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ErrorMessage from '../../components/ui/ErrorMessage';
import * as templateService from '../../services/templateService';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock the template service
jest.mock('../../services/templateService');
const mockTemplateService = templateService as jest.Mocked<typeof templateService>;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

const mockTemplate = {
  id: 'test-template-id',
  name: 'Test Template',
  description: 'A test template for accessibility testing',
  template_type: 'medical',
  schema: ['firstName', 'lastName', 'email', 'phoneNumber', 'description'],
  storage_path: '/templates/test.docx',
  is_active: true,
  tenant_id: 'test-tenant',
  created_by: 'test-user',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

describe('Accessibility Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('DynamicField Component', () => {
    test('should have no accessibility violations for text input', async () => {
      const { container } = render(
        <DynamicField
          name="firstName"
          label="First Name"
          type="text"
          value=""
          onChange={() => {}}
          required={true}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations for email input', async () => {
      const { container } = render(
        <DynamicField
          name="email"
          label="Email Address"
          type="email"
          value=""
          onChange={() => {}}
          required={true}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations for textarea', async () => {
      const { container } = render(
        <DynamicField
          name="description"
          label="Description"
          type="textarea"
          value=""
          onChange={() => {}}
          required={true}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have no accessibility violations with error state', async () => {
      const { container } = render(
        <DynamicField
          name="email"
          label="Email Address"
          type="email"
          value="invalid-email"
          onChange={() => {}}
          error="Please enter a valid email address"
          required={true}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have proper ARIA attributes', () => {
      render(
        <DynamicField
          name="email"
          label="Email Address"
          type="email"
          value="invalid-email"
          onChange={() => {}}
          error="Please enter a valid email address"
          required={true}
        />
      );

      const input = screen.getByLabelText('Email Address *');
      const errorMessage = screen.getByText('Please enter a valid email address');

      expect(input).toHaveAttribute('aria-invalid', 'true');
      expect(input).toHaveAttribute('aria-describedby');
      expect(errorMessage).toHaveAttribute('role', 'alert');
      expect(errorMessage).toHaveAttribute('aria-live', 'polite');
    });

    test('should have proper label association', () => {
      render(
        <DynamicField
          name="firstName"
          label="First Name"
          type="text"
          value=""
          onChange={() => {}}
          required={true}
        />
      );

      const input = screen.getByLabelText('First Name *');
      const label = screen.getByText('First Name');

      expect(input).toHaveAttribute('id');
      expect(label).toHaveAttribute('for', input.getAttribute('id'));
    });
  });

  describe('LoadingSpinner Component', () => {
    test('should have no accessibility violations', async () => {
      const { container } = render(
        <LoadingSpinner message="Loading template..." />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have proper ARIA attributes', () => {
      render(<LoadingSpinner message="Loading template..." />);

      const spinner = screen.getByRole('status');
      expect(spinner).toHaveAttribute('aria-live', 'polite');
      expect(screen.getByText('Loading template...', { selector: '.sr-only' })).toBeInTheDocument();
    });
  });

  describe('ErrorMessage Component', () => {
    test('should have no accessibility violations', async () => {
      const { container } = render(
        <ErrorMessage
          title="Error"
          message="Something went wrong"
          onRetry={() => {}}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have proper ARIA attributes', () => {
      render(
        <ErrorMessage
          title="Error"
          message="Something went wrong"
          onRetry={() => {}}
        />
      );

      const errorContainer = screen.getByRole('alert');
      expect(errorContainer).toHaveAttribute('aria-live', 'assertive');
    });

    test('should have accessible buttons', () => {
      const mockRetry = jest.fn();
      const mockDismiss = jest.fn();

      render(
        <ErrorMessage
          title="Error"
          message="Something went wrong"
          onRetry={mockRetry}
          onDismiss={mockDismiss}
        />
      );

      const retryButton = screen.getByText('Try Again');
      const dismissButton = screen.getByText('Dismiss');

      expect(retryButton).toHaveAttribute('type', 'button');
      expect(dismissButton).toHaveAttribute('type', 'button');
    });
  });

  describe('TemplateForm Component', () => {
    test('should have no accessibility violations when loaded', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      const { container } = render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have proper form structure', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      const form = screen.getByRole('form');
      expect(form).toBeInTheDocument();

      // Check that all form fields are properly labeled
      expect(screen.getByLabelText('First Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Last Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Email *')).toBeInTheDocument();
      expect(screen.getByLabelText('Phone Number *')).toBeInTheDocument();
      expect(screen.getByLabelText('Description *')).toBeInTheDocument();
    });

    test('should have accessible submit button', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      const submitButton = screen.getByText('Generate Document');
      expect(submitButton).toHaveAttribute('type', 'submit');
      expect(submitButton).toHaveAttribute('disabled');
    });

    test('should have accessible progress indicator', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '0');
      expect(progressBar).toHaveAttribute('aria-valuemin', '0');
      expect(progressBar).toHaveAttribute('aria-valuemax', '100');
    });
  });

  describe('Keyboard Navigation', () => {
    test('should support tab navigation', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      const firstNameField = screen.getByLabelText('First Name *');
      const lastNameField = screen.getByLabelText('Last Name *');
      const emailField = screen.getByLabelText('Email *');

      // Test tab order
      firstNameField.focus();
      expect(document.activeElement).toBe(firstNameField);

      // Simulate tab key
      firstNameField.blur();
      lastNameField.focus();
      expect(document.activeElement).toBe(lastNameField);

      lastNameField.blur();
      emailField.focus();
      expect(document.activeElement).toBe(emailField);
    });
  });

  describe('Screen Reader Support', () => {
    test('should have proper heading structure', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      const mainHeading = screen.getByRole('heading', { level: 2 });
      expect(mainHeading).toHaveTextContent('Test Template');
    });

    test('should announce form validation errors', async () => {
      mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);

      render(
        <TemplateForm
          templateId="test-template-id"
          onSuccess={() => {}}
          onError={() => {}}
        />
      );

      // Wait for template to load
      await screen.findByText('Test Template');

      // Try to submit form without filling required fields
      const submitButton = screen.getByText('Generate Document');
      
      // Enable submit button by filling one field
      const firstNameField = screen.getByLabelText('First Name *');
      firstNameField.focus();
      firstNameField.blur();

      // Check that error messages have proper ARIA attributes
      const errorMessages = screen.queryAllByRole('alert');
      errorMessages.forEach(error => {
        expect(error).toHaveAttribute('aria-live', 'polite');
      });
    });
  });
});
