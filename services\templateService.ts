/**
 * Template service for Supabase API operations
 */

import { supabase, getCurrentUser } from './supabaseClient';
import { Template } from '../types/template';
import { SupabaseResponse, CreateReportRequest, CreateReportResponse } from '../types/api';

/**
 * Fetches template metadata by ID
 * @param templateId - The template ID to fetch
 * @returns Template data or throws error
 */
export async function fetchTemplate(templateId: string): Promise<Template> {
  try {
    // Ensure user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Fetch template from Supabase
    const { data, error } = await supabase
      .from('document_templates')
      .select('*')
      .eq('id', templateId)
      .eq('is_active', true)
      .single();

    if (error) {
      throw new Error(`Failed to fetch template: ${error.message}`);
    }

    if (!data) {
      throw new Error('Template not found');
    }

    // Transform and validate the response
    const template: Template = {
      id: data.id,
      name: data.name,
      description: data.description,
      template_type: data.template_type,
      schema: Array.isArray(data.schema) ? data.schema : [],
      storage_path: data.storage_path,
      is_active: data.is_active,
      tenant_id: data.tenant_id,
      created_by: data.created_by,
      created_at: data.created_at,
      updated_at: data.updated_at
    };

    return template;
  } catch (error) {
    console.error('Error fetching template:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred');
  }
}

/**
 * Submits form data to create a report
 * @param request - The create report request
 * @returns Create report response
 */
export async function createReport(request: CreateReportRequest): Promise<CreateReportResponse> {
  try {
    // Ensure user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Make API call to create report endpoint
    const response = await fetch('/api/create-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user.access_token || ''}`
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result: CreateReportResponse = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to create report');
    }

    return result;
  } catch (error) {
    console.error('Error creating report:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred');
  }
}

/**
 * Lists all templates for the current user's tenant
 * @returns Array of templates
 */
export async function listTemplates(): Promise<Template[]> {
  try {
    // Ensure user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Fetch templates from Supabase
    const { data, error } = await supabase
      .from('document_templates')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch templates: ${error.message}`);
    }

    // Transform the response
    const templates: Template[] = (data || []).map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      template_type: item.template_type,
      schema: Array.isArray(item.schema) ? item.schema : [],
      storage_path: item.storage_path,
      is_active: item.is_active,
      tenant_id: item.tenant_id,
      created_by: item.created_by,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    return templates;
  } catch (error) {
    console.error('Error listing templates:', error);
    throw error instanceof Error ? error : new Error('Unknown error occurred');
  }
}

/**
 * Validates template schema and sanitizes field names
 * @param schema - Raw schema array from database
 * @returns Cleaned array of field names
 */
export function sanitizeTemplateSchema(schema: string[]): string[] {
  if (!Array.isArray(schema)) {
    console.warn('Invalid schema format, falling back to empty array');
    return [];
  }

  return schema
    .map(field => {
      if (typeof field !== 'string') {
        return '';
      }
      // Remove double curly braces and trim whitespace
      return field.replace(/^\{\{|\}\}$/g, '').trim();
    })
    .filter(field => field.length > 0);
}

/**
 * Checks if user has access to a specific template
 * @param templateId - Template ID to check
 * @returns True if user has access, false otherwise
 */
export async function hasTemplateAccess(templateId: string): Promise<boolean> {
  try {
    const template = await fetchTemplate(templateId);
    return !!template;
  } catch (error) {
    return false;
  }
}
