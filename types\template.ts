/**
 * TypeScript interfaces for template and form-related types
 */

export interface Template {
  id: string;
  name: string;
  description?: string;
  template_type?: string;
  schema: string[];
  storage_path: string;
  is_active: boolean;
  tenant_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface FormData {
  [fieldName: string]: string;
}

export interface TemplateFormProps {
  templateId: string;
  onSuccess?: (reportId: string) => void;
  onError?: (error: Error) => void;
  className?: string;
}

export interface DynamicFieldProps {
  name: string;
  label: string;
  type: 'text' | 'email' | 'tel' | 'url' | 'number' | 'date' | 'textarea';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  error?: string;
  required?: boolean;
  placeholder?: string;
  className?: string;
}

export interface FieldTypeDetectionResult {
  type: 'text' | 'email' | 'tel' | 'url' | 'number' | 'date' | 'textarea';
  placeholder?: string;
  step?: string;
  pattern?: string;
}

export interface FormValidationError {
  field: string;
  message: string;
}

export interface DraftData {
  templateId: string;
  formData: FormData;
  lastSaved: string;
}
