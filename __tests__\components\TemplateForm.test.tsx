/**
 * Integration tests for TemplateForm component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import TemplateForm from '../../components/TemplateForm';
import * as templateService from '../../services/templateService';

// Mock the template service
jest.mock('../../services/templateService');
const mockTemplateService = templateService as jest.Mocked<typeof templateService>;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

const mockTemplate = {
  id: 'test-template-id',
  name: 'Test Template',
  description: 'A test template',
  template_type: 'medical',
  schema: ['firstName', 'lastName', 'email', 'age', 'description'],
  storage_path: '/templates/test.docx',
  is_active: true,
  tenant_id: 'test-tenant',
  created_by: 'test-user',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

describe('TemplateForm', () => {
  const defaultProps = {
    templateId: 'test-template-id',
    onSuccess: jest.fn(),
    onError: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  test('should render loading state initially', () => {
    mockTemplateService.fetchTemplate.mockImplementation(() => new Promise(() => {}));
    
    render(<TemplateForm {...defaultProps} />);
    
    expect(screen.getByText('Loading template...')).toBeInTheDocument();
  });

  test('should render form fields after loading template', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    // Check that form fields are rendered
    expect(screen.getByLabelText('First Name *')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name *')).toBeInTheDocument();
    expect(screen.getByLabelText('Email *')).toBeInTheDocument();
    expect(screen.getByLabelText('Age *')).toBeInTheDocument();
    expect(screen.getByLabelText('Description *')).toBeInTheDocument();
  });

  test('should detect correct field types', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    // Check field types
    const emailField = screen.getByLabelText('Email *');
    const ageField = screen.getByLabelText('Age *');
    const descriptionField = screen.getByLabelText('Description *');

    expect(emailField).toHaveAttribute('type', 'email');
    expect(ageField).toHaveAttribute('type', 'number');
    expect(descriptionField.tagName).toBe('TEXTAREA');
  });

  test('should validate required fields', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    const submitButton = screen.getByText('Generate Document');
    
    // Try to submit without filling fields
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });
  });

  test('should update progress as fields are filled', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Progress: 0%')).toBeInTheDocument();
    });

    const firstNameField = screen.getByLabelText('First Name *');
    
    await userEvent.type(firstNameField, 'John');

    await waitFor(() => {
      expect(screen.getByText('Progress: 20%')).toBeInTheDocument();
    });
  });

  test('should save draft to localStorage', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    const firstNameField = screen.getByLabelText('First Name *');
    await userEvent.type(firstNameField, 'John');

    // Wait for auto-save (mocked)
    await waitFor(() => {
      expect(mockLocalStorage.setItem).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  test('should load draft from localStorage', async () => {
    const draftData = {
      templateId: 'test-template-id',
      formData: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        age: '30',
        description: 'Test description'
      },
      lastSaved: '2023-01-01T00:00:00Z'
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(draftData));
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    });
  });

  test('should submit form successfully', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    mockTemplateService.createReport.mockResolvedValue({
      success: true,
      reportId: 'test-report-id',
      downloadUrl: '/api/reports/test-report-id/download'
    });
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    // Fill all required fields
    await userEvent.type(screen.getByLabelText('First Name *'), 'John');
    await userEvent.type(screen.getByLabelText('Last Name *'), 'Doe');
    await userEvent.type(screen.getByLabelText('Email *'), '<EMAIL>');
    await userEvent.type(screen.getByLabelText('Age *'), '30');
    await userEvent.type(screen.getByLabelText('Description *'), 'Test description');

    const submitButton = screen.getByText('Generate Document');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockTemplateService.createReport).toHaveBeenCalledWith({
        templateId: 'test-template-id',
        formData: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          age: '30',
          description: 'Test description'
        }
      });
    });

    expect(defaultProps.onSuccess).toHaveBeenCalledWith('test-report-id');
  });

  test('should handle form submission error', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    mockTemplateService.createReport.mockRejectedValue(new Error('Submission failed'));
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    // Fill all required fields
    await userEvent.type(screen.getByLabelText('First Name *'), 'John');
    await userEvent.type(screen.getByLabelText('Last Name *'), 'Doe');
    await userEvent.type(screen.getByLabelText('Email *'), '<EMAIL>');
    await userEvent.type(screen.getByLabelText('Age *'), '30');
    await userEvent.type(screen.getByLabelText('Description *'), 'Test description');

    const submitButton = screen.getByText('Generate Document');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(defaultProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('should clear form when clear button is clicked', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    // Fill a field
    const firstNameField = screen.getByLabelText('First Name *');
    await userEvent.type(firstNameField, 'John');

    expect(firstNameField).toHaveValue('John');

    // Clear form
    const clearButton = screen.getByText('Clear Form');
    fireEvent.click(clearButton);

    expect(firstNameField).toHaveValue('');
  });

  test('should toggle preview mode', async () => {
    mockTemplateService.fetchTemplate.mockResolvedValue(mockTemplate);
    
    render(<TemplateForm {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Template')).toBeInTheDocument();
    });

    const previewButton = screen.getByText('Show Preview');
    fireEvent.click(previewButton);

    expect(screen.getByText('Form Data Preview')).toBeInTheDocument();
    expect(screen.getByText('Hide Preview')).toBeInTheDocument();
  });
});
