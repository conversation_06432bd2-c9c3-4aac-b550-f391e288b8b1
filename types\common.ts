/**
 * Common shared TypeScript types
 */

export type TaskState = 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETE' | 'CANCELLED';

export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  tenant_id: string;
  role: string;
  created_at: string;
}

export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  is_active: boolean;
  created_at: string;
}

export type FormFieldType = 
  | 'text' 
  | 'email' 
  | 'tel' 
  | 'url' 
  | 'number' 
  | 'date' 
  | 'textarea';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
}

export interface FormField {
  name: string;
  label: string;
  type: FormFieldType;
  value: string;
  error?: string;
  validation?: ValidationRule;
  placeholder?: string;
}

export interface ProgressInfo {
  completed: number;
  total: number;
  percentage: number;
}
