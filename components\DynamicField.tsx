/**
 * Dynamic form field component with type detection and validation
 */

import React, { useId } from 'react';
import { DynamicFieldProps } from '../types/template';

const DynamicField: React.FC<DynamicFieldProps> = ({
  name,
  label,
  type,
  value,
  onChange,
  onBlur,
  error,
  required = true,
  placeholder,
  className = ''
}) => {
  const fieldId = useId();
  const errorId = useId();

  const baseInputClasses = `
    block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm 
    ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 
    focus:ring-2 focus:ring-inset focus:ring-blue-600 
    sm:text-sm sm:leading-6 transition-colors
    ${error ? 'ring-red-300 focus:ring-red-600' : ''}
  `;

  const textareaClasses = `
    block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm 
    ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 
    focus:ring-2 focus:ring-inset focus:ring-blue-600 
    sm:text-sm sm:leading-6 transition-colors resize-vertical
    ${error ? 'ring-red-300 focus:ring-red-600' : ''}
  `;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const renderField = () => {
    const commonProps = {
      id: fieldId,
      name,
      value,
      onChange: handleChange,
      onBlur,
      placeholder,
      required,
      'aria-invalid': !!error,
      'aria-describedby': error ? errorId : undefined,
    };

    switch (type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows={4}
            className={textareaClasses}
          />
        );

      case 'email':
        return (
          <input
            {...commonProps}
            type="email"
            pattern="[^@]+@[^@]+\.[^@]+"
            className={baseInputClasses}
          />
        );

      case 'tel':
        return (
          <input
            {...commonProps}
            type="tel"
            className={baseInputClasses}
          />
        );

      case 'url':
        return (
          <input
            {...commonProps}
            type="url"
            className={baseInputClasses}
          />
        );

      case 'number':
        return (
          <input
            {...commonProps}
            type="number"
            step="any"
            className={baseInputClasses}
          />
        );

      case 'date':
        return (
          <input
            {...commonProps}
            type="date"
            className={baseInputClasses}
          />
        );

      default:
        return (
          <input
            {...commonProps}
            type="text"
            className={baseInputClasses}
          />
        );
    }
  };

  return (
    <div className={`space-y-1 ${className}`}>
      <label 
        htmlFor={fieldId}
        className="block text-sm font-medium leading-6 text-gray-900"
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {renderField()}
      
      {error && (
        <p 
          id={errorId}
          className="text-sm text-red-600"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  );
};

export default DynamicField;
