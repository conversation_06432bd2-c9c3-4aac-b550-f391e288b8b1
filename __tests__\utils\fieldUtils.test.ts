/**
 * Tests for field utility functions
 */

import {
  detectFieldType,
  formatFieldLabel,
  validateFieldValue,
  calculateProgress,
  generatePlaceholder
} from '../../utils/fieldUtils';

describe('detectFieldType', () => {
  test('should detect date fields correctly', () => {
    expect(detectFieldType('birthDate').type).toBe('date');
    expect(detectFieldType('createdAt').type).toBe('date');
    expect(detectFieldType('expiryDate').type).toBe('date');
    expect(detectFieldType('appointment_time').type).toBe('date');
  });

  test('should detect email fields correctly', () => {
    expect(detectFieldType('email').type).toBe('email');
    expect(detectFieldType('userEmail').type).toBe('email');
    expect(detectFieldType('contact_mail').type).toBe('email');
  });

  test('should detect phone fields correctly', () => {
    expect(detectFieldType('phone').type).toBe('tel');
    expect(detectFieldType('mobileNumber').type).toBe('tel');
    expect(detectFieldType('telephone').type).toBe('tel');
  });

  test('should detect textarea fields correctly', () => {
    expect(detectFieldType('description').type).toBe('textarea');
    expect(detectFieldType('medicalHistory').type).toBe('textarea');
    expect(detectFieldType('observations').type).toBe('textarea');
    expect(detectFieldType('notes').type).toBe('textarea');
  });

  test('should detect number fields correctly', () => {
    expect(detectFieldType('age').type).toBe('number');
    expect(detectFieldType('amount').type).toBe('number');
    expect(detectFieldType('quantity').type).toBe('number');
    expect(detectFieldType('price').type).toBe('number');
  });

  test('should detect URL fields correctly', () => {
    expect(detectFieldType('website').type).toBe('url');
    expect(detectFieldType('profileUrl').type).toBe('url');
    expect(detectFieldType('link').type).toBe('url');
  });

  test('should default to text for unknown fields', () => {
    expect(detectFieldType('unknownField').type).toBe('text');
    expect(detectFieldType('randomName').type).toBe('text');
  });
});

describe('formatFieldLabel', () => {
  test('should format camelCase correctly', () => {
    expect(formatFieldLabel('firstName')).toBe('First Name');
    expect(formatFieldLabel('lastName')).toBe('Last Name');
    expect(formatFieldLabel('dateOfBirth')).toBe('Date Of Birth');
  });

  test('should format snake_case correctly', () => {
    expect(formatFieldLabel('first_name')).toBe('First Name');
    expect(formatFieldLabel('last_name')).toBe('Last Name');
    expect(formatFieldLabel('date_of_birth')).toBe('Date Of Birth');
  });

  test('should format kebab-case correctly', () => {
    expect(formatFieldLabel('first-name')).toBe('First Name');
    expect(formatFieldLabel('last-name')).toBe('Last Name');
    expect(formatFieldLabel('date-of-birth')).toBe('Date Of Birth');
  });

  test('should handle single words correctly', () => {
    expect(formatFieldLabel('name')).toBe('Name');
    expect(formatFieldLabel('email')).toBe('Email');
    expect(formatFieldLabel('age')).toBe('Age');
  });

  test('should handle mixed cases correctly', () => {
    expect(formatFieldLabel('firstName_lastName')).toBe('First Name Last Name');
    expect(formatFieldLabel('user-emailAddress')).toBe('User Email Address');
  });
});

describe('validateFieldValue', () => {
  test('should validate required fields', () => {
    expect(validateFieldValue('', 'text', true)).toBe('This field is required');
    expect(validateFieldValue('  ', 'text', true)).toBe('This field is required');
    expect(validateFieldValue('value', 'text', true)).toBeNull();
  });

  test('should allow empty optional fields', () => {
    expect(validateFieldValue('', 'text', false)).toBeNull();
    expect(validateFieldValue('  ', 'email', false)).toBeNull();
  });

  test('should validate email fields', () => {
    expect(validateFieldValue('invalid-email', 'email', false)).toBe('Please enter a valid email address');
    expect(validateFieldValue('<EMAIL>', 'email', false)).toBeNull();
    expect(validateFieldValue('<EMAIL>', 'email', false)).toBeNull();
  });

  test('should validate URL fields', () => {
    expect(validateFieldValue('invalid-url', 'url', false)).toBe('Please enter a valid URL');
    expect(validateFieldValue('https://example.com', 'url', false)).toBeNull();
    expect(validateFieldValue('http://localhost:3000', 'url', false)).toBeNull();
  });

  test('should validate number fields', () => {
    expect(validateFieldValue('not-a-number', 'number', false)).toBe('Please enter a valid number');
    expect(validateFieldValue('123', 'number', false)).toBeNull();
    expect(validateFieldValue('123.45', 'number', false)).toBeNull();
    expect(validateFieldValue('-123', 'number', false)).toBeNull();
  });

  test('should validate phone fields', () => {
    expect(validateFieldValue('123', 'tel', false)).toBe('Please enter a valid phone number');
    expect(validateFieldValue('1234567890', 'tel', false)).toBeNull();
    expect(validateFieldValue('+****************', 'tel', false)).toBeNull();
  });

  test('should validate date fields', () => {
    expect(validateFieldValue('invalid-date', 'date', false)).toBe('Please enter a valid date');
    expect(validateFieldValue('2023-12-25', 'date', false)).toBeNull();
    expect(validateFieldValue('12/25/2023', 'date', false)).toBeNull();
  });
});

describe('calculateProgress', () => {
  test('should calculate progress correctly', () => {
    const formData = {
      field1: 'value1',
      field2: 'value2',
      field3: '',
      field4: '  ',
      field5: 'value5'
    };

    expect(calculateProgress(formData, 5)).toBe(60); // 3 out of 5 fields filled
    expect(calculateProgress({}, 5)).toBe(0);
    expect(calculateProgress(formData, 0)).toBe(0);
  });

  test('should handle edge cases', () => {
    expect(calculateProgress({}, 0)).toBe(0);
    expect(calculateProgress({ field1: 'value' }, 1)).toBe(100);
  });
});

describe('generatePlaceholder', () => {
  test('should generate appropriate placeholders', () => {
    expect(generatePlaceholder('firstName', 'text')).toBe('Enter first name');
    expect(generatePlaceholder('email', 'email')).toBe('Enter email');
    expect(generatePlaceholder('birthDate', 'date')).toBe('Select birth date');
    expect(generatePlaceholder('description', 'textarea')).toBe('Enter description');
  });
});
