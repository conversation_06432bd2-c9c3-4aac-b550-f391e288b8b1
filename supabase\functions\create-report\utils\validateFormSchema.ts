/**
 * Form validation utilities for the create-report Edge Function
 */

import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { DocumentTemplate, ValidationError, FormValidationResult, ErrorCodes } from './types.ts';

/**
 * Validate UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Sanitize form data to prevent injection attacks
 */
export function sanitizeFormData(formData: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(formData)) {
    // Sanitize key name
    const sanitizedKey = key.trim().replace(/[^\w\-_.]/g, '');
    
    if (!sanitizedKey) {
      continue; // Skip invalid keys
    }

    // Sanitize value based on type
    if (typeof value === 'string') {
      // Remove control characters and limit length
      sanitized[sanitizedKey] = value
        .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
        .trim()
        .substring(0, 10000); // Limit to 10KB per field
    } else if (typeof value === 'number') {
      // Validate number is finite
      sanitized[sanitizedKey] = Number.isFinite(value) ? value : 0;
    } else if (typeof value === 'boolean') {
      sanitized[sanitizedKey] = Boolean(value);
    } else if (value === null || value === undefined) {
      sanitized[sanitizedKey] = null;
    } else if (Array.isArray(value)) {
      // Sanitize array elements (limit to 100 items)
      sanitized[sanitizedKey] = value
        .slice(0, 100)
        .map(item => typeof item === 'string' ? 
          item.replace(/[\x00-\x1F\x7F]/g, '').trim().substring(0, 1000) : 
          item
        );
    } else if (typeof value === 'object') {
      // For objects, convert to JSON string and sanitize
      try {
        const jsonString = JSON.stringify(value).substring(0, 5000);
        sanitized[sanitizedKey] = JSON.parse(jsonString);
      } catch {
        sanitized[sanitizedKey] = null;
      }
    } else {
      // Convert unknown types to string
      sanitized[sanitizedKey] = String(value).substring(0, 1000);
    }
  }

  return sanitized;
}

/**
 * Get template and validate it exists and is accessible
 */
export async function getAndValidateTemplate(
  supabase: SupabaseClient,
  templateId: string,
  tenantId: string
): Promise<DocumentTemplate> {
  // Validate UUID format
  if (!isValidUUID(templateId)) {
    const error = new Error('Invalid template ID format');
    (error as any).code = ErrorCodes.INVALID_REQUEST;
    (error as any).status = 400;
    throw error;
  }

  // Query template with tenant validation
  const { data: template, error: templateError } = await supabase
    .from('document_templates')
    .select('*')
    .eq('id', templateId)
    .eq('tenant_id', tenantId)
    .single();

  if (templateError) {
    console.error('Template query error:', templateError);
    
    if (templateError.code === 'PGRST116') { // No rows returned
      const notFoundError = new Error('Template not found or access denied');
      (notFoundError as any).code = ErrorCodes.TEMPLATE_NOT_FOUND;
      (notFoundError as any).status = 404;
      throw notFoundError;
    }
    
    const dbError = new Error(`Database error: ${templateError.message}`);
    (dbError as any).code = ErrorCodes.DATABASE_ERROR;
    (dbError as any).status = 500;
    throw dbError;
  }

  if (!template) {
    const notFoundError = new Error('Template not found');
    (notFoundError as any).code = ErrorCodes.TEMPLATE_NOT_FOUND;
    (notFoundError as any).status = 404;
    throw notFoundError;
  }

  if (!template.is_active) {
    const inactiveError = new Error('Template is inactive');
    (inactiveError as any).code = ErrorCodes.INACTIVE_TEMPLATE;
    (inactiveError as any).status = 403;
    throw inactiveError;
  }

  return template as DocumentTemplate;
}

/**
 * Validate form data against template schema
 */
export function validateFormDataAgainstSchema(
  formData: Record<string, any>,
  templateSchema: string[]
): FormValidationResult {
  const errors: ValidationError[] = [];
  const missingFields: string[] = [];
  
  // Check for required fields from template schema
  for (const requiredField of templateSchema) {
    const fieldValue = formData[requiredField];
    
    if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
      missingFields.push(requiredField);
      errors.push({
        field: requiredField,
        message: `Required field '${requiredField}' is missing or empty`,
        code: 'REQUIRED_FIELD_MISSING',
      });
    }
  }

  // Validate field names don't contain dangerous characters
  for (const fieldName of Object.keys(formData)) {
    if (!/^[a-zA-Z0-9_\-\.]+$/.test(fieldName)) {
      errors.push({
        field: fieldName,
        message: `Field name '${fieldName}' contains invalid characters`,
        code: 'INVALID_FIELD_NAME',
      });
    }
  }

  // Check for excessively large data
  const formDataSize = JSON.stringify(formData).length;
  if (formDataSize > 1024 * 1024) { // 1MB limit
    errors.push({
      field: '_form',
      message: 'Form data exceeds maximum size limit (1MB)',
      code: 'FORM_DATA_TOO_LARGE',
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    missingFields,
  };
}

/**
 * Comprehensive form validation
 */
export async function validateFormSubmission(
  supabase: SupabaseClient,
  templateId: string,
  formData: Record<string, any>,
  tenantId: string
): Promise<{ template: DocumentTemplate; sanitizedData: Record<string, any> }> {
  // Get and validate template
  const template = await getAndValidateTemplate(supabase, templateId, tenantId);
  
  // Sanitize form data
  const sanitizedData = sanitizeFormData(formData);
  
  // Validate against template schema
  const validationResult = validateFormDataAgainstSchema(sanitizedData, template.schema);
  
  if (!validationResult.isValid) {
    const validationError = new Error('Form validation failed');
    (validationError as any).code = ErrorCodes.VALIDATION_FAILED;
    (validationError as any).status = 422;
    (validationError as any).details = {
      errors: validationResult.errors,
      missingFields: validationResult.missingFields,
    };
    throw validationError;
  }

  return { template, sanitizedData };
}
