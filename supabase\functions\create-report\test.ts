/**
 * Test suite for the create-report Edge Function
 * 
 * Run with: deno test --allow-net --allow-env supabase/functions/create-report/test.ts
 */

import { assertEquals, assertExists } from 'https://deno.land/std@0.168.0/testing/asserts.ts';

// Test configuration
const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || 'http://localhost:54321';
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY') || '';
const TEST_EMAIL = Deno.env.get('TEST_EMAIL') || '<EMAIL>';
const TEST_PASSWORD = Deno.env.get('TEST_PASSWORD') || 'testpassword123';

// Test data
const VALID_TEMPLATE_ID = '550e8400-e29b-41d4-a716-446655440000';
const INVALID_TEMPLATE_ID = '00000000-0000-0000-0000-000000000000';

interface TestResponse {
  status: number;
  body: any;
  headers: Headers;
}

/**
 * Helper function to make HTTP requests
 */
async function makeRequest(
  method: string,
  path: string,
  body?: any,
  headers: Record<string, string> = {}
): Promise<TestResponse> {
  const url = `${SUPABASE_URL}${path}`;
  const requestHeaders = {
    'Content-Type': 'application/json',
    'apikey': SUPABASE_ANON_KEY,
    ...headers,
  };

  const response = await fetch(url, {
    method,
    headers: requestHeaders,
    body: body ? JSON.stringify(body) : undefined,
  });

  let responseBody;
  try {
    responseBody = await response.json();
  } catch {
    responseBody = await response.text();
  }

  return {
    status: response.status,
    body: responseBody,
    headers: response.headers,
  };
}

/**
 * Get JWT token for testing
 */
async function getAuthToken(): Promise<string> {
  const response = await makeRequest('POST', '/auth/v1/token?grant_type=password', {
    email: TEST_EMAIL,
    password: TEST_PASSWORD,
  });

  if (response.status !== 200) {
    throw new Error(`Authentication failed: ${JSON.stringify(response.body)}`);
  }

  return response.body.access_token;
}

/**
 * Test successful report creation
 */
Deno.test('Create Report - Success', async () => {
  const token = await getAuthToken();
  
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: VALID_TEMPLATE_ID,
      formData: {
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        orderDate: '2025-01-28',
        orderTotal: 1250.00,
      },
    },
    { Authorization: `Bearer ${token}` }
  );

  assertEquals(response.status, 201);
  assertExists(response.body.reportId);
  assertEquals(response.body.status, 'pending');
  assertExists(response.body.message);
  assertExists(response.body.estimatedProcessingTime);
});

/**
 * Test missing authentication
 */
Deno.test('Create Report - Missing Authentication', async () => {
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: VALID_TEMPLATE_ID,
      formData: { customerName: 'John Doe' },
    }
  );

  assertEquals(response.status, 401);
  assertEquals(response.body.code, 'AUTHENTICATION_REQUIRED');
  assertExists(response.body.error);
});

/**
 * Test invalid template ID format
 */
Deno.test('Create Report - Invalid Template ID Format', async () => {
  const token = await getAuthToken();
  
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: 'invalid-uuid',
      formData: { customerName: 'John Doe' },
    },
    { Authorization: `Bearer ${token}` }
  );

  assertEquals(response.status, 400);
  assertEquals(response.body.code, 'INVALID_REQUEST');
});

/**
 * Test template not found
 */
Deno.test('Create Report - Template Not Found', async () => {
  const token = await getAuthToken();
  
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: INVALID_TEMPLATE_ID,
      formData: { customerName: 'John Doe' },
    },
    { Authorization: `Bearer ${token}` }
  );

  assertEquals(response.status, 404);
  assertEquals(response.body.code, 'TEMPLATE_NOT_FOUND');
});

/**
 * Test missing required fields
 */
Deno.test('Create Report - Missing Required Fields', async () => {
  const token = await getAuthToken();
  
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: VALID_TEMPLATE_ID,
      formData: {
        customerEmail: '<EMAIL>', // Missing customerName
      },
    },
    { Authorization: `Bearer ${token}` }
  );

  assertEquals(response.status, 422);
  assertEquals(response.body.code, 'VALIDATION_FAILED');
  assertExists(response.body.details.errors);
  assertExists(response.body.details.missingFields);
});

/**
 * Test invalid JSON body
 */
Deno.test('Create Report - Invalid JSON', async () => {
  const token = await getAuthToken();
  const url = `${SUPABASE_URL}/functions/v1/create-report`;
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'apikey': SUPABASE_ANON_KEY,
      'Authorization': `Bearer ${token}`,
    },
    body: 'invalid json',
  });

  const body = await response.json();
  assertEquals(response.status, 400);
  assertEquals(body.code, 'INVALID_REQUEST');
});

/**
 * Test method not allowed
 */
Deno.test('Create Report - Method Not Allowed', async () => {
  const token = await getAuthToken();
  
  const response = await makeRequest(
    'GET',
    '/functions/v1/create-report',
    undefined,
    { Authorization: `Bearer ${token}` }
  );

  assertEquals(response.status, 405);
  assertEquals(response.body.code, 'INVALID_REQUEST');
});

/**
 * Test CORS preflight request
 */
Deno.test('Create Report - CORS Preflight', async () => {
  const response = await makeRequest('OPTIONS', '/functions/v1/create-report');
  
  assertEquals(response.status, 200);
  assertExists(response.headers.get('Access-Control-Allow-Origin'));
  assertExists(response.headers.get('Access-Control-Allow-Methods'));
});

/**
 * Test form data sanitization
 */
Deno.test('Create Report - Form Data Sanitization', async () => {
  const token = await getAuthToken();
  
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: VALID_TEMPLATE_ID,
      formData: {
        customerName: 'John Doe\x00\x1F', // Control characters should be removed
        customerEmail: '<EMAIL>',
        'invalid-field-name!@#': 'should be rejected',
        validField: '<script>alert("xss")</script>', // Should be sanitized
      },
    },
    { Authorization: `Bearer ${token}` }
  );

  // Should either succeed with sanitized data or fail with validation error
  if (response.status === 422) {
    assertEquals(response.body.code, 'VALIDATION_FAILED');
  } else {
    assertEquals(response.status, 201);
  }
});

/**
 * Test large form data
 */
Deno.test('Create Report - Large Form Data', async () => {
  const token = await getAuthToken();
  
  // Create large form data (over 1MB)
  const largeData = 'x'.repeat(1024 * 1024 + 1);
  
  const response = await makeRequest(
    'POST',
    '/functions/v1/create-report',
    {
      templateId: VALID_TEMPLATE_ID,
      formData: {
        customerName: 'John Doe',
        largeField: largeData,
      },
    },
    { Authorization: `Bearer ${token}` }
  );

  assertEquals(response.status, 422);
  assertEquals(response.body.code, 'VALIDATION_FAILED');
});

console.log('All tests defined. Run with: deno test --allow-net --allow-env');
