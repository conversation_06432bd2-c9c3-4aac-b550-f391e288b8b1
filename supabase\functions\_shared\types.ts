/**
 * Shared TypeScript interfaces and types for Supabase Edge Functions
 * Used across multiple functions for consistency and type safety
 */

// Database model interfaces
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  plan: 'free' | 'pro' | 'enterprise';
  max_users: number;
  max_documents: number;
  max_storage_gb: number;
  settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  tenant_id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  role: 'admin' | 'user' | 'viewer';
  is_active: boolean;
  last_login_at: string | null;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface DocumentTemplate {
  id: string;
  tenant_id: string;
  name: string;
  description: string | null;
  template_type: string | null;
  schema: string[];
  storage_path: string;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Document {
  id: string;
  tenant_id: string;
  template_id: string | null;
  name: string;
  status: 'draft' | 'pending' | 'processing' | 'completed' | 'failed';
  input_data: Record<string, any>;
  generated_file_path: string | null;
  pdf_file_path: string | null;
  file_size_bytes: number;
  processing_started_at: string | null;
  processing_completed_at: string | null;
  error_message: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Create Report API Types
export interface CreateReportRequest {
  templateId: string;
  formData: Record<string, any>;
}

export interface CreateReportResponse {
  reportId: string;
  status: 'pending';
  message: string;
  estimatedProcessingTime: string;
}

export interface CreateReportErrorResponse {
  error: string;
  details: string | object;
  code: string;
}

// Authentication Types
export interface AuthenticatedUser {
  id: string;
  tenant_id: string;
  email: string;
  role: string;
}

// Validation Types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  missingFields: string[];
}

export interface StorageUsage {
  id: string;
  tenant_id: string;
  total_size_bytes: number;
  document_count: number;
  last_calculated_at: string;
  created_at: string;
  updated_at: string;
}

// API request/response interfaces
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
}

// Authentication interfaces
export interface AuthenticatedUser {
  id: string;
  tenant_id: string;
  email: string;
  role: 'admin' | 'user' | 'viewer';
}

export interface JWTPayload {
  sub: string;
  email: string;
  aud: string;
  exp: number;
  iat: number;
  iss: string;
  user_metadata?: Record<string, any>;
  app_metadata?: Record<string, any>;
}

// Upload template specific interfaces
export interface UploadTemplateRequest {
  file: File;
  name: string;
  templateType?: string | undefined;
}

export interface UploadTemplateResponse {
  success: boolean;
  data?: {
    id: string;
    tenant_id: string;
    name: string;
    template_type: string | null;
    schema: string[];
    storage_path: string;
    created_at: string;
  };
  error?: ApiError;
}

// Document processing interfaces
export interface ProcessDocumentRequest {
  template_id: string;
  name: string;
  input_data: Record<string, any>;
}

export interface ProcessDocumentResponse {
  success: boolean;
  data?: {
    id: string;
    tenant_id: string;
    template_id: string;
    name: string;
    status: string;
    created_at: string;
  };
  error?: ApiError;
}

// Storage interfaces
export interface StorageFile {
  name: string;
  id: string;
  updated_at: string;
  created_at: string;
  last_accessed_at: string;
  metadata: {
    eTag: string;
    size: number;
    mimetype: string;
    cacheControl: string;
    lastModified: string;
    contentLength: number;
    httpStatusCode: number;
  };
}

// Validation interfaces
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface FileValidationOptions {
  maxSize: number;
  allowedMimeTypes: string[];
  allowedExtensions: string[];
}

// Utility types
export type DatabaseInsert<T> = Omit<T, 'id' | 'created_at' | 'updated_at'>;
export type DatabaseUpdate<T> = Partial<Omit<T, 'id' | 'created_at' | 'updated_at'>> & {
  updated_at?: string;
};

// Error codes enum
export enum ErrorCodes {
  // Authentication errors
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // Validation errors
  INVALID_INPUT = 'INVALID_INPUT',
  INVALID_FILE = 'INVALID_FILE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  UNSUPPORTED_FILE_TYPE = 'UNSUPPORTED_FILE_TYPE',
  
  // Business logic errors
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  TEMPLATE_NOT_FOUND = 'TEMPLATE_NOT_FOUND',
  DUPLICATE_NAME = 'DUPLICATE_NAME',
  
  // System errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
  
  // HTTP errors
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMITED = 'RATE_LIMITED',
}

// HTTP status codes
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  PAYLOAD_TOO_LARGE = 413,
  UNSUPPORTED_MEDIA_TYPE = 415,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

// Content types
export enum ContentTypes {
  JSON = 'application/json',
  FORM_DATA = 'multipart/form-data',
  DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  PDF = 'application/pdf',
  TEXT = 'text/plain',
  HTML = 'text/html',
}

// Storage bucket names
export enum StorageBuckets {
  TEMPLATES = 'templates',
  DOCUMENTS = 'documents',
  EXPORTS = 'exports',
  AVATARS = 'avatars',
}
