/**
 * Progress indicator component with accessibility support
 */

import React from 'react';

interface ProgressIndicatorProps {
  progress: number; // 0-100
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'yellow' | 'red';
  className?: string;
  label?: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  showPercentage = true,
  size = 'md',
  color = 'blue',
  className = '',
  label = 'Progress'
}) => {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.max(0, Math.min(100, progress));

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const colorClasses = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    yellow: 'bg-yellow-600',
    red: 'bg-red-600'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Label and percentage */}
      <div className="flex items-center justify-between">
        <span className={`font-medium text-gray-700 ${textSizeClasses[size]}`}>
          {label}
        </span>
        {showPercentage && (
          <span className={`text-gray-600 ${textSizeClasses[size]}`}>
            {normalizedProgress}%
          </span>
        )}
      </div>

      {/* Progress bar */}
      <div 
        className={`w-full bg-gray-200 rounded-full ${sizeClasses[size]}`}
        role="progressbar"
        aria-valuenow={normalizedProgress}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label={`${label}: ${normalizedProgress}% complete`}
      >
        <div
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${normalizedProgress}%` }}
        />
      </div>

      {/* Screen reader text */}
      <span className="sr-only">
        {label}: {normalizedProgress} percent complete
      </span>
    </div>
  );
};

export default ProgressIndicator;
