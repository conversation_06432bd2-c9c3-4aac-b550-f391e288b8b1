/**
 * Form validation utilities
 */

import { FormData, FormValidationError } from '../types/template';
import { validateFieldValue } from './fieldUtils';
import { FormFieldType } from '../types/common';

/**
 * Validates all form fields
 * @param formData - The form data to validate
 * @param fieldTypes - Map of field names to their types
 * @param requiredFields - Array of required field names
 * @returns Array of validation errors
 */
export function validateForm(
  formData: FormData,
  fieldTypes: Record<string, FormFieldType>,
  requiredFields: string[] = []
): FormValidationError[] {
  const errors: FormValidationError[] = [];

  // Check each field
  Object.keys(fieldTypes).forEach(fieldName => {
    const value = formData[fieldName] || '';
    const type = fieldTypes[fieldName];
    const isRequired = requiredFields.includes(fieldName);

    const error = validateFieldValue(value, type, isRequired);
    if (error) {
      errors.push({
        field: fieldName,
        message: error
      });
    }
  });

  return errors;
}

/**
 * Checks if the form is valid (no validation errors)
 * @param formData - The form data to validate
 * @param fieldTypes - Map of field names to their types
 * @param requiredFields - Array of required field names
 * @returns True if form is valid, false otherwise
 */
export function isFormValid(
  formData: FormData,
  fieldTypes: Record<string, FormFieldType>,
  requiredFields: string[] = []
): boolean {
  const errors = validateForm(formData, fieldTypes, requiredFields);
  return errors.length === 0;
}

/**
 * Gets validation error for a specific field
 * @param fieldName - Name of the field
 * @param value - Field value
 * @param type - Field type
 * @param required - Whether field is required
 * @returns Error message or null if valid
 */
export function getFieldError(
  fieldName: string,
  value: string,
  type: FormFieldType,
  required: boolean = true
): string | null {
  return validateFieldValue(value, type, required);
}
