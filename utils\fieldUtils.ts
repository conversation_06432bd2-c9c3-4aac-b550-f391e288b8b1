/**
 * Utility functions for field type detection and label formatting
 */

import { FieldTypeDetectionResult, FormFieldType } from '../types/template';

/**
 * Detects the appropriate field type based on field name patterns
 * @param fieldName - The name of the field to analyze
 * @returns Field type detection result with type and additional properties
 */
export function detectFieldType(fieldName: string): FieldTypeDetectionResult {
  const lowerName = fieldName.toLowerCase();

  // Date fields
  if (lowerName.includes('date') || 
      lowerName.includes('created') || 
      lowerName.includes('updated') || 
      lowerName.includes('time') || 
      lowerName.includes('birth') || 
      lowerName.includes('expiry')) {
    return {
      type: 'date',
      placeholder: 'Select date'
    };
  }

  // Email fields
  if (lowerName.includes('email') || lowerName.includes('mail')) {
    return {
      type: 'email',
      pattern: '[^@]+@[^@]+\\.[^@]+',
      placeholder: 'Enter email address'
    };
  }

  // Phone fields
  if (lowerName.includes('phone') || 
      lowerName.includes('mobile') || 
      lowerName.includes('tel')) {
    return {
      type: 'tel',
      placeholder: 'Enter phone number'
    };
  }

  // Large text fields
  if (lowerName.includes('history') || 
      lowerName.includes('observation') || 
      lowerName.includes('conclusion') || 
      lowerName.includes('description') || 
      lowerName.includes('notes') || 
      lowerName.includes('comments') || 
      lowerName.includes('summary') || 
      lowerName.includes('details')) {
    return {
      type: 'textarea',
      placeholder: 'Enter detailed information'
    };
  }

  // Number fields
  if (lowerName.includes('number') || 
      lowerName.includes('amount') || 
      lowerName.includes('quantity') || 
      lowerName.includes('count') || 
      lowerName.includes('age') || 
      lowerName.includes('price') || 
      lowerName.includes('cost')) {
    return {
      type: 'number',
      step: 'any',
      placeholder: 'Enter number'
    };
  }

  // URL fields
  if (lowerName.includes('url') || 
      lowerName.includes('website') || 
      lowerName.includes('link')) {
    return {
      type: 'url',
      placeholder: 'Enter URL'
    };
  }

  // Default to text
  return {
    type: 'text',
    placeholder: 'Enter text'
  };
}

/**
 * Converts field names to human-readable labels using title case transformation
 * @param fieldName - The field name to convert
 * @returns Human-readable label
 */
export function formatFieldLabel(fieldName: string): string {
  // Handle camelCase and PascalCase
  const withSpaces = fieldName.replace(/([a-z])([A-Z])/g, '$1 $2');
  
  // Handle snake_case and kebab-case
  const normalized = withSpaces.replace(/[_-]/g, ' ');
  
  // Convert to title case
  return normalized
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Validates a field value based on its type
 * @param value - The value to validate
 * @param type - The field type
 * @param required - Whether the field is required
 * @returns Error message if validation fails, null if valid
 */
export function validateFieldValue(
  value: string, 
  type: FormFieldType, 
  required: boolean = true
): string | null {
  // Check required fields
  if (required && (!value || value.trim() === '')) {
    return 'This field is required';
  }

  // Skip validation for empty optional fields
  if (!value || value.trim() === '') {
    return null;
  }

  // Type-specific validation
  switch (type) {
    case 'email':
      const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address';
      }
      break;

    case 'url':
      try {
        new URL(value);
      } catch {
        return 'Please enter a valid URL';
      }
      break;

    case 'number':
      if (isNaN(Number(value))) {
        return 'Please enter a valid number';
      }
      break;

    case 'tel':
      // Basic phone validation - at least 10 digits
      const phoneRegex = /\d{10,}/;
      if (!phoneRegex.test(value.replace(/\D/g, ''))) {
        return 'Please enter a valid phone number';
      }
      break;

    case 'date':
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return 'Please enter a valid date';
      }
      break;
  }

  return null;
}

/**
 * Calculates form completion percentage
 * @param formData - Current form data
 * @param totalFields - Total number of fields
 * @returns Completion percentage (0-100)
 */
export function calculateProgress(formData: Record<string, string>, totalFields: number): number {
  if (totalFields === 0) return 0;
  
  const filledFields = Object.values(formData).filter(value => 
    value && value.trim() !== ''
  ).length;
  
  return Math.round((filledFields / totalFields) * 100);
}

/**
 * Generates a placeholder text based on field name and type
 * @param fieldName - The field name
 * @param type - The field type
 * @returns Appropriate placeholder text
 */
export function generatePlaceholder(fieldName: string, type: FormFieldType): string {
  const label = formatFieldLabel(fieldName);
  
  switch (type) {
    case 'email':
      return `Enter ${label.toLowerCase()}`;
    case 'tel':
      return `Enter ${label.toLowerCase()}`;
    case 'url':
      return `Enter ${label.toLowerCase()}`;
    case 'number':
      return `Enter ${label.toLowerCase()}`;
    case 'date':
      return `Select ${label.toLowerCase()}`;
    case 'textarea':
      return `Enter ${label.toLowerCase()}`;
    default:
      return `Enter ${label.toLowerCase()}`;
  }
}
