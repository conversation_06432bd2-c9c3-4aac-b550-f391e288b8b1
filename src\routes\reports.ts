/**
 * Reports API routes
 */

import express from 'express';
import { createReportController } from '../controllers/reportController';
import { authenticateUser } from '../middleware/auth';
import { validateCreateReport } from '../middleware/validation';

const router = express.Router();

// POST /api/create-report - Create a new report from template
router.post('/create-report', 
  authenticateUser,
  validateCreateReport,
  createReportController
);

export default router;
