/**
 * Supabase Edge Function: Create Report
 * 
 * Handles form submissions for document generation, validates data,
 * stores records, and enqueues background processing jobs.
 * 
 * Endpoint: POST /functions/v1/create-report
 * Authentication: Required (JWT Bearer token)
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { corsHeaders } from '../_shared/cors.ts';
import {
  CreateReportRequestBody,
  CreateReportSuccessResponse,
  ErrorCodes,
  HttpStatus,
  ProcessingTimeEstimates
} from './utils/types.ts';
import { getAuthenticatedUser, createErrorResponse, handleError } from './utils/auth.ts';
import { validateFormSubmission } from './utils/validateFormSchema.ts';
import { triggerBackgroundProcessing, estimateProcessingTime } from './utils/backgroundJobs.ts';
import {
  createReportCreationAuditLog,
  createValidationFailureAuditLog,
  createAuthFailureAuditLog
} from './utils/auditLog.ts';

/**
 * Validate request body structure
 */
function validateRequestBody(body: any): CreateReportRequestBody {
  if (!body || typeof body !== 'object') {
    throw new Error('Request body must be a valid JSON object');
  }

  const { templateId, formData } = body;

  if (!templateId || typeof templateId !== 'string') {
    throw new Error('templateId is required and must be a string');
  }

  if (!formData || typeof formData !== 'object' || Array.isArray(formData)) {
    throw new Error('formData is required and must be an object');
  }

  return { templateId, formData };
}

/**
 * Create document record in database
 */
async function createDocumentRecord(
  supabase: any,
  templateId: string,
  tenantId: string,
  userId: string,
  sanitizedData: Record<string, any>,
  templateName: string
): Promise<string> {
  const documentName = `${templateName} - ${new Date().toISOString().split('T')[0]}`;
  
  const { data: document, error: insertError } = await supabase
    .from('documents')
    .insert({
      tenant_id: tenantId,
      template_id: templateId,
      name: documentName,
      status: 'pending',
      input_data: sanitizedData,
      created_by: userId,
    })
    .select('id')
    .single();

  if (insertError) {
    console.error('Document creation error:', insertError);
    throw new Error(`Failed to create document record: ${insertError.message}`);
  }

  if (!document?.id) {
    throw new Error('Document creation failed - no ID returned');
  }

  return document.id;
}





/**
 * Main request handler
 */
serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return createErrorResponse(
      'Method not allowed',
      'Only POST requests are supported',
      ErrorCodes.INVALID_REQUEST,
      405
    );
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration');
      return createErrorResponse(
        'Internal server error',
        'Service configuration error',
        ErrorCodes.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      global: {
        headers: { Authorization: req.headers.get('Authorization')! },
      },
    });

    // Parse and validate request body
    let requestBody: CreateReportRequestBody;
    try {
      const rawBody = await req.json();
      requestBody = validateRequestBody(rawBody);
    } catch (error) {
      return createErrorResponse(
        'Invalid request body',
        error instanceof Error ? error.message : 'Request body validation failed',
        ErrorCodes.INVALID_REQUEST,
        HttpStatus.BAD_REQUEST
      );
    }

    // Authenticate user and get tenant information
    let user;
    try {
      user = await getAuthenticatedUser(supabase, req);
    } catch (error: any) {
      // Create audit log for authentication failure
      await createAuthFailureAuditLog(supabase, req, error.message || 'Authentication failed');

      return createErrorResponse(
        'Authentication failed',
        error.message || 'Invalid or missing authentication',
        error.code || ErrorCodes.AUTHENTICATION_REQUIRED,
        error.status || HttpStatus.UNAUTHORIZED
      );
    }

    // Validate form submission and get template
    let template, sanitizedData;
    try {
      const validationResult = await validateFormSubmission(
        supabase,
        requestBody.templateId,
        requestBody.formData,
        user.tenant_id
      );
      template = validationResult.template;
      sanitizedData = validationResult.sanitizedData;
    } catch (error: any) {
      // Create audit log for validation failure
      await createValidationFailureAuditLog(
        supabase,
        user.tenant_id,
        requestBody.templateId,
        req,
        error.details || { message: error.message }
      );

      return createErrorResponse(
        'Validation failed',
        error.details || error.message || 'Form validation failed',
        error.code || ErrorCodes.VALIDATION_FAILED,
        error.status || HttpStatus.UNPROCESSABLE_ENTITY
      );
    }

    // Create document record
    let documentId: string;
    try {
      documentId = await createDocumentRecord(
        supabase,
        requestBody.templateId,
        user.tenant_id,
        user.id,
        sanitizedData,
        template.name
      );
    } catch (error) {
      console.error('Document creation failed:', error);
      return createErrorResponse(
        'Failed to create report',
        error instanceof Error ? error.message : 'Document creation failed',
        ErrorCodes.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Trigger background processing (non-blocking)
    await triggerBackgroundProcessing(
      supabase,
      documentId,
      requestBody.templateId,
      user.tenant_id,
      sanitizedData
    );

    // Create audit log (non-blocking)
    await createReportCreationAuditLog(
      supabase,
      user.tenant_id,
      documentId,
      requestBody.templateId,
      req,
      {
        template_name: template.name,
        form_field_count: Object.keys(sanitizedData).length,
        estimated_processing_time: estimateProcessingTime(sanitizedData, template.schema),
      }
    );

    // Estimate processing time based on complexity
    const estimatedTime = estimateProcessingTime(sanitizedData, template.schema);

    // Return success response
    const response: CreateReportSuccessResponse = {
      reportId: documentId,
      status: 'pending',
      message: 'Report successfully created and queued for processing.',
      estimatedProcessingTime: estimatedTime,
    };

    return new Response(JSON.stringify(response), {
      status: HttpStatus.CREATED,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });

  } catch (error) {
    console.error('Unexpected error in create-report function:', error);
    
    return createErrorResponse(
      'Internal server error',
      'An unexpected error occurred while processing your request',
      ErrorCodes.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});
