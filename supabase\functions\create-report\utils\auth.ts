/**
 * Authentication and authorization utilities for the create-report Edge Function
 */

import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { UserProfile, ErrorCodes, HttpStatus } from './types.ts';

/**
 * Extract and validate JW<PERSON> token from Authorization header
 */
export function extractAuthToken(request: Request): string {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader) {
    throw new Error('Missing Authorization header');
  }

  if (!authHeader.startsWith('Bearer ')) {
    throw new Error('Invalid Authorization header format. Expected: Bearer <token>');
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  
  if (!token || token.trim().length === 0) {
    throw new Error('Empty or invalid JWT token');
  }

  return token;
}

/**
 * Get authenticated user profile with tenant information
 */
export async function getAuthenticatedUser(
  supabase: SupabaseClient,
  request: Request
): Promise<UserProfile> {
  try {
    // Extract token from Authorization header
    const token = extractAuthToken(request);

    // Get user from JWT token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError) {
      console.error('Auth error:', authError);
      throw new Error(`Authentication failed: ${authError.message}`);
    }

    if (!user) {
      throw new Error('No user found in JWT token');
    }

    // Get user profile with tenant information
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('id, tenant_id, email, full_name, role, is_active')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Profile query error:', profileError);
      throw new Error(`Failed to fetch user profile: ${profileError.message}`);
    }

    if (!profile) {
      throw new Error('User profile not found');
    }

    if (!profile.is_active) {
      throw new Error('User account is inactive');
    }

    if (!profile.tenant_id) {
      throw new Error('User is not associated with a tenant');
    }

    return profile as UserProfile;
  } catch (error) {
    console.error('Authentication error:', error);
    
    // Re-throw with appropriate error code
    if (error instanceof Error) {
      if (error.message.includes('Authentication failed') || 
          error.message.includes('JWT') ||
          error.message.includes('Authorization header')) {
        const authError = new Error(error.message);
        (authError as any).code = ErrorCodes.AUTHENTICATION_REQUIRED;
        (authError as any).status = HttpStatus.UNAUTHORIZED;
        throw authError;
      }
      
      if (error.message.includes('User profile not found') ||
          error.message.includes('not associated with a tenant')) {
        const tenantError = new Error(error.message);
        (tenantError as any).code = ErrorCodes.TENANT_NOT_FOUND;
        (tenantError as any).status = HttpStatus.FORBIDDEN;
        throw tenantError;
      }
      
      if (error.message.includes('inactive')) {
        const inactiveError = new Error('User account is inactive');
        (inactiveError as any).code = ErrorCodes.AUTHENTICATION_REQUIRED;
        (inactiveError as any).status = HttpStatus.FORBIDDEN;
        throw inactiveError;
      }
    }

    // Default to authentication required for unknown errors
    const defaultError = new Error('Authentication failed');
    (defaultError as any).code = ErrorCodes.AUTHENTICATION_REQUIRED;
    (defaultError as any).status = HttpStatus.UNAUTHORIZED;
    throw defaultError;
  }
}

/**
 * Validate that a template belongs to the user's tenant
 */
export async function validateTemplateAccess(
  supabase: SupabaseClient,
  templateId: string,
  tenantId: string
): Promise<boolean> {
  try {
    const { data: template, error } = await supabase
      .from('document_templates')
      .select('id, tenant_id, is_active')
      .eq('id', templateId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      console.error('Template access validation error:', error);
      return false;
    }

    return template && template.is_active;
  } catch (error) {
    console.error('Template access validation error:', error);
    return false;
  }
}

/**
 * Create error response with proper structure and CORS headers
 */
export function createErrorResponse(
  error: string,
  details: string | object,
  code: string,
  status: number = HttpStatus.BAD_REQUEST
): Response {
  const errorResponse = {
    error,
    details,
    code,
    timestamp: new Date().toISOString(),
  };

  // Import CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
  };

  return new Response(JSON.stringify(errorResponse), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders,
    },
  });
}

/**
 * Enhanced error handling with specific error types
 */
export function handleError(error: any): Response {
  console.error('Error occurred:', error);

  // Handle known error types with specific codes
  if (error.code) {
    switch (error.code) {
      case ErrorCodes.AUTHENTICATION_REQUIRED:
        return createErrorResponse(
          'Authentication required',
          error.message || 'Valid JWT token required',
          ErrorCodes.AUTHENTICATION_REQUIRED,
          HttpStatus.UNAUTHORIZED
        );

      case ErrorCodes.TEMPLATE_NOT_FOUND:
        return createErrorResponse(
          'Template not found',
          error.message || 'The specified template does not exist or is not accessible',
          ErrorCodes.TEMPLATE_NOT_FOUND,
          HttpStatus.NOT_FOUND
        );

      case ErrorCodes.TEMPLATE_ACCESS_DENIED:
        return createErrorResponse(
          'Access denied',
          error.message || 'You do not have permission to access this template',
          ErrorCodes.TEMPLATE_ACCESS_DENIED,
          HttpStatus.FORBIDDEN
        );

      case ErrorCodes.VALIDATION_FAILED:
        return createErrorResponse(
          'Validation failed',
          error.details || error.message || 'Form data validation failed',
          ErrorCodes.VALIDATION_FAILED,
          HttpStatus.UNPROCESSABLE_ENTITY
        );

      case ErrorCodes.TENANT_NOT_FOUND:
        return createErrorResponse(
          'Tenant not found',
          error.message || 'User is not associated with a valid tenant',
          ErrorCodes.TENANT_NOT_FOUND,
          HttpStatus.FORBIDDEN
        );

      case ErrorCodes.INACTIVE_TEMPLATE:
        return createErrorResponse(
          'Template inactive',
          error.message || 'The specified template is not active',
          ErrorCodes.INACTIVE_TEMPLATE,
          HttpStatus.FORBIDDEN
        );

      case ErrorCodes.DATABASE_ERROR:
        return createErrorResponse(
          'Database error',
          'A database error occurred while processing your request',
          ErrorCodes.DATABASE_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );

      default:
        return createErrorResponse(
          'Internal error',
          'An unexpected error occurred',
          ErrorCodes.INTERNAL_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
    }
  }

  // Handle generic errors
  if (error instanceof Error) {
    if (error.message.includes('JSON') || error.message.includes('parse')) {
      return createErrorResponse(
        'Invalid request format',
        'Request body must be valid JSON',
        ErrorCodes.INVALID_REQUEST,
        HttpStatus.BAD_REQUEST
      );
    }

    if (error.message.includes('required') || error.message.includes('missing')) {
      return createErrorResponse(
        'Missing required fields',
        error.message,
        ErrorCodes.MISSING_REQUIRED_FIELDS,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  // Default error response
  return createErrorResponse(
    'Internal server error',
    'An unexpected error occurred while processing your request',
    ErrorCodes.INTERNAL_ERROR,
    HttpStatus.INTERNAL_SERVER_ERROR
  );
}
