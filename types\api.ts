/**
 * TypeScript interfaces for API responses and requests
 */

export interface CreateReportRequest {
  templateId: string;
  formData: Record<string, string>;
}

export interface CreateReportResponse {
  success: boolean;
  reportId?: string;
  downloadUrl?: string;
  error?: string;
}

export interface ApiError {
  error: string;
  message: string;
  statusCode?: number;
}

export interface SupabaseResponse<T> {
  data: T | null;
  error: {
    message: string;
    details?: string;
    hint?: string;
    code?: string;
  } | null;
}

export interface TemplateResponse {
  id: string;
  name: string;
  description?: string;
  template_type?: string;
  schema: string[];
  storage_path: string;
  is_active: boolean;
  tenant_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
  details?: string;
}
