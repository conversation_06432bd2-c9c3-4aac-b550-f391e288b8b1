# Dynamic Form Generator Component

A comprehensive React component for generating dynamic forms from document templates with advanced features including auto-save, validation, accessibility, and document generation.

## Features

### Core Functionality
- **Dynamic Form Generation**: Automatically generates form fields based on template schema
- **Intelligent Field Type Detection**: Detects appropriate input types (text, email, phone, date, textarea, number, URL) based on field names
- **Real-time Validation**: Client-side validation with immediate feedback
- **Draft Auto-save**: Automatically saves form progress to localStorage every 30 seconds
- **Progress Tracking**: Visual progress indicator showing completion percentage

### User Experience
- **Live Preview**: Toggle-able preview of form data in formatted or JSON view
- **Keyboard Navigation**: Full keyboard accessibility with arrow keys and tab navigation
- **Responsive Design**: Mobile-friendly responsive layout
- **Loading States**: Smooth loading animations and error handling
- **Clear Form**: One-click form reset functionality

### Accessibility (WCAG 2.1 AA Compliant)
- **Screen Reader Support**: Proper ARIA labels, roles, and live regions
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Logical tab order and focus indicators
- **Error Announcements**: Screen reader announcements for validation errors
- **High Contrast**: Accessible color schemes and focus indicators

### Technical Features
- **TypeScript**: Full type safety with comprehensive interfaces
- **Supabase Integration**: Secure API communication with Row Level Security
- **Multi-tenant Architecture**: Tenant isolation and security
- **Error Handling**: Comprehensive error handling and user feedback
- **Testing**: Unit tests, integration tests, and accessibility tests

## Installation

### Prerequisites
```bash
# Install React and Next.js dependencies
npm install react react-dom next @types/react @types/react-dom

# Install Tailwind CSS
npm install tailwindcss postcss autoprefixer @tailwindcss/forms

# Install Supabase client
npm install @supabase/supabase-js

# Install testing dependencies (optional)
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-axe
```

### Setup
1. Configure Tailwind CSS (see `tailwind.config.js`)
2. Add global styles (see `styles/globals.css`)
3. Set up Supabase environment variables:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

## Usage

### Basic Usage
```tsx
import TemplateForm from './components/TemplateForm';

function MyPage() {
  const handleSuccess = (reportId: string) => {
    console.log('Document generated:', reportId);
    // Redirect to download page or show success message
  };

  const handleError = (error: Error) => {
    console.error('Form submission failed:', error);
    // Show error message to user
  };

  return (
    <TemplateForm
      templateId="your-template-id"
      onSuccess={handleSuccess}
      onError={handleError}
      className="max-w-4xl mx-auto"
    />
  );
}
```

### Advanced Usage with Custom Styling
```tsx
<TemplateForm
  templateId="medical-report-template"
  onSuccess={(reportId) => {
    // Custom success handling
    router.push(`/reports/${reportId}`);
  }}
  onError={(error) => {
    // Custom error handling
    toast.error(error.message);
  }}
  className="bg-white shadow-lg rounded-lg p-6"
/>
```

## Component Architecture

### Main Components
- **TemplateForm**: Main form component with all functionality
- **DynamicField**: Individual form field component with type detection
- **LoadingSpinner**: Accessible loading indicator
- **ErrorMessage**: Comprehensive error display component
- **ProgressIndicator**: Visual progress tracking
- **FormPreview**: Live preview of form data

### Utility Functions
- **fieldUtils.ts**: Field type detection and validation
- **validation.ts**: Form validation logic
- **storage.ts**: Draft management with localStorage
- **templateService.ts**: Supabase API communication

## Field Type Detection

The component automatically detects appropriate field types based on field names:

| Field Name Pattern | Detected Type | Example Fields |
|-------------------|---------------|----------------|
| date, time, birth, expiry | date | birthDate, createdAt, expiryDate |
| email, mail | email | email, userEmail, contactMail |
| phone, mobile, tel | tel | phone, mobileNumber, telephone |
| history, observation, notes, description | textarea | medicalHistory, notes, description |
| number, amount, age, price, count | number | age, amount, quantity, price |
| url, website, link | url | website, profileUrl, link |
| * (default) | text | firstName, lastName, title |

## API Integration

### Backend Requirements
The component expects a backend API endpoint at `/api/create-report` that:
- Accepts POST requests with `templateId` and `formData`
- Returns a response with `success`, `reportId`, and optional `downloadUrl`
- Handles authentication and tenant isolation

### Database Schema
Required Supabase tables:
- `document_templates`: Template metadata with schema array
- `documents`: Generated document records
- `user_profiles`: User tenant associations

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific test suites
npm test -- --testPathPattern=fieldUtils
npm test -- --testPathPattern=TemplateForm
npm test -- --testPathPattern=accessibility

# Run tests with coverage
npm test -- --coverage
```

### Test Coverage
- **Unit Tests**: Field detection, validation, utility functions
- **Integration Tests**: Form submission, API communication, user interactions
- **Accessibility Tests**: WCAG compliance, keyboard navigation, screen reader support

## Accessibility Features

### WCAG 2.1 AA Compliance
- ✅ Keyboard Navigation
- ✅ Screen Reader Support
- ✅ Focus Management
- ✅ Color Contrast
- ✅ Error Identification
- ✅ Labels and Instructions
- ✅ Status Messages

### Keyboard Shortcuts
- **Tab/Shift+Tab**: Navigate between fields
- **Arrow Keys**: Navigate between fields (when not in input)
- **Ctrl/Cmd+Enter**: Submit form
- **Escape**: Clear focus or close preview

## Performance Considerations

### Optimization Features
- **Auto-save Debouncing**: Prevents excessive localStorage writes
- **Lazy Loading**: Components load only when needed
- **Memoization**: React.memo and useCallback for performance
- **Efficient Re-renders**: Optimized state updates

### Bundle Size
- **Tree Shaking**: Only imports used utilities
- **Code Splitting**: Separate chunks for different features
- **Minimal Dependencies**: Lightweight external dependencies

## Security

### Data Protection
- **Input Sanitization**: All user inputs are validated and sanitized
- **XSS Prevention**: Proper escaping of user-generated content
- **CSRF Protection**: Secure API communication
- **Tenant Isolation**: Row Level Security in Supabase

### Authentication
- **JWT Validation**: Secure token-based authentication
- **Session Management**: Automatic token refresh
- **Access Control**: Template-level permissions

## Browser Support

### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Progressive Enhancement
- **Core Functionality**: Works without JavaScript (basic form)
- **Enhanced Features**: Auto-save, validation, preview with JavaScript
- **Accessibility**: Full accessibility regardless of JavaScript state

## Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run development server: `npm run dev`
5. Run tests: `npm test`

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Airbnb configuration with accessibility rules
- **Prettier**: Consistent code formatting
- **Testing**: Minimum 80% test coverage required

## License

MIT License - see LICENSE file for details.

## Support

For issues, questions, or contributions, please:
1. Check existing GitHub issues
2. Create a new issue with detailed description
3. Include browser version and steps to reproduce
4. Provide minimal reproduction example if possible
