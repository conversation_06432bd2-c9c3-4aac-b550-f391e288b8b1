# Frontend Components

This directory contains React components for the Doc Maker App frontend.

## Structure

- `TemplateForm.tsx` - Main dynamic form generator component
- `DynamicField.tsx` - Individual form field component
- `ui/` - Reusable UI components
  - `LoadingSpinner.tsx` - Loading indicator
  - `ErrorMessage.tsx` - Error display component

## Usage

Components are built with React 18+ and TypeScript, using Tailwind CSS for styling.
