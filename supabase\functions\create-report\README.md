# Create Report Edge Function

A comprehensive Supabase Edge Function for handling document generation requests with background processing queue.

## Overview

This Edge Function handles form submissions for document generation, validates data against template schemas, stores records in the database, and triggers background processing jobs.

## API Specification

### Endpoint
- **URL**: `/functions/v1/create-report`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Authentication**: Required (JWT Bearer token)

### Request Schema

```typescript
{
  "templateId": string (UUID format),
  "formData": Record<string, any> // Dynamic key-value pairs matching template schema
}
```

### Response Schema

#### Success Response (201 Created)
```json
{
  "reportId": "550e8400-e29b-41d4-a716-************",
  "status": "pending",
  "message": "Report successfully created and queued for processing.",
  "estimatedProcessingTime": "2-5 minutes"
}
```

#### Error Response
```json
{
  "error": "Validation failed",
  "details": {
    "errors": [
      {
        "field": "customerName",
        "message": "Required field 'customerName' is missing or empty",
        "code": "REQUIRED_FIELD_MISSING"
      }
    ],
    "missingFields": ["customerName"]
  },
  "code": "VALIDATION_FAILED",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

## Testing Examples

### Prerequisites

1. **Get your Supabase credentials**:
   - Project URL: `https://your-project.supabase.co`
   - Anon Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

2. **Authenticate and get JWT token**:
   ```bash
   # Login to get JWT token
   curl -X POST 'https://your-project.supabase.co/auth/v1/token?grant_type=password' \
     -H 'apikey: YOUR_ANON_KEY' \
     -H 'Content-Type: application/json' \
     -d '{
       "email": "<EMAIL>",
       "password": "your-password"
     }'
   ```

3. **Create a test template** (use the upload-template function first)

### Test Cases

#### 1. Successful Report Creation

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "templateId": "550e8400-e29b-41d4-a716-************",
    "formData": {
      "customerName": "John Doe",
      "customerEmail": "<EMAIL>",
      "orderDate": "2025-01-28",
      "orderTotal": 1250.00,
      "items": [
        {"name": "Product A", "quantity": 2, "price": 500.00},
        {"name": "Product B", "quantity": 1, "price": 250.00}
      ]
    }
  }'
```

**Expected Response** (201 Created):
```json
{
  "reportId": "123e4567-e89b-12d3-a456-426614174000",
  "status": "pending",
  "message": "Report successfully created and queued for processing.",
  "estimatedProcessingTime": "2-5 minutes"
}
```

#### 2. Missing Required Fields

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "templateId": "550e8400-e29b-41d4-a716-************",
    "formData": {
      "customerEmail": "<EMAIL>",
      "orderDate": "2025-01-28"
    }
  }'
```

**Expected Response** (422 Unprocessable Entity):
```json
{
  "error": "Validation failed",
  "details": {
    "errors": [
      {
        "field": "customerName",
        "message": "Required field 'customerName' is missing or empty",
        "code": "REQUIRED_FIELD_MISSING"
      }
    ],
    "missingFields": ["customerName"]
  },
  "code": "VALIDATION_FAILED",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

#### 3. Invalid Template ID

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "templateId": "invalid-uuid",
    "formData": {
      "customerName": "John Doe"
    }
  }'
```

**Expected Response** (400 Bad Request):
```json
{
  "error": "Invalid request",
  "details": "Invalid template ID format",
  "code": "INVALID_REQUEST",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

#### 4. Template Not Found

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "templateId": "00000000-0000-0000-0000-000000000000",
    "formData": {
      "customerName": "John Doe"
    }
  }'
```

**Expected Response** (404 Not Found):
```json
{
  "error": "Template not found",
  "details": "Template not found or access denied",
  "code": "TEMPLATE_NOT_FOUND",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

#### 5. Missing Authentication

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Content-Type: application/json' \
  -d '{
    "templateId": "550e8400-e29b-41d4-a716-************",
    "formData": {
      "customerName": "John Doe"
    }
  }'
```

**Expected Response** (401 Unauthorized):
```json
{
  "error": "Authentication required",
  "details": "Missing Authorization header",
  "code": "AUTHENTICATION_REQUIRED",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

#### 6. Invalid JSON Body

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d 'invalid json'
```

**Expected Response** (400 Bad Request):
```json
{
  "error": "Invalid request body",
  "details": "Request body must be valid JSON",
  "code": "INVALID_REQUEST",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

#### 7. Method Not Allowed

```bash
curl -X GET 'https://your-project.supabase.co/functions/v1/create-report' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

**Expected Response** (405 Method Not Allowed):
```json
{
  "error": "Method not allowed",
  "details": "Only POST requests are supported",
  "code": "INVALID_REQUEST",
  "timestamp": "2025-01-28T10:30:00.000Z"
}
```

## Error Codes Reference

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_REQUEST` | 400 | Invalid JSON payload or missing required fields |
| `AUTHENTICATION_REQUIRED` | 401 | Missing or invalid authentication token |
| `TEMPLATE_ACCESS_DENIED` | 403 | Template doesn't belong to user's tenant |
| `TEMPLATE_NOT_FOUND` | 404 | Template not found |
| `VALIDATION_FAILED` | 422 | Form data validation failures |
| `MISSING_REQUIRED_FIELDS` | 422 | Specific required fields are missing |
| `INACTIVE_TEMPLATE` | 403 | Template exists but is not active |
| `TENANT_NOT_FOUND` | 403 | User is not associated with a tenant |
| `DATABASE_ERROR` | 500 | Database or system errors |
| `INTERNAL_ERROR` | 500 | Unexpected server errors |

## Processing Time Estimates

The function provides dynamic processing time estimates based on form complexity:

- **Simple forms** (≤5 fields, ≤10 schema fields, <1KB data): "1-2 minutes"
- **Standard forms** (≤15 fields, ≤25 schema fields, <10KB data): "2-5 minutes"  
- **Complex forms** (>15 fields, >25 schema fields, >10KB data): "5-10 minutes"

## Background Processing

The function triggers background processing using multiple fallback methods:

1. **Primary**: Supabase Realtime channel `document-processing`
2. **Fallback 1**: Database job queue (if `processing_jobs` table exists)
3. **Fallback 2**: External webhook (if `PROCESSING_WEBHOOK_URL` is configured)

## Audit Logging

All operations are logged to the `audit_logs` table with:
- User IP address and User-Agent
- Action type and resource information
- Request metadata and processing context
- Validation failures and authentication attempts

## Security Features

- JWT token validation with tenant isolation
- Input sanitization and validation
- SQL injection prevention
- Rate limiting support (audit logs track attempts)
- Row Level Security (RLS) policy enforcement
